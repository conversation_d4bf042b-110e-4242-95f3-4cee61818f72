# 软件权限管理系统 - 桌面版

## 概述

这是一个基于 Electron 的桌面应用程序，将原本的 Web 应用打包成了独立的桌面应用。用户可以直接运行 exe 文件，无需手动启动浏览器访问。

## 文件结构

```
License_Management/
├── dist-packager/
│   └── license-management-desktop-win32-x64/
│       ├── license-management-desktop.exe  # 主程序
│       ├── resources/                      # 应用资源
│       └── ...                            # 其他运行时文件
├── main.js                                # Electron 主进程
├── package.json                           # 项目配置
├── start-desktop.bat                      # 开发启动脚本
└── backend/                               # 后端服务
    ├── license-management.exe             # 后端服务器
    ├── public/                            # 前端页面
    │   ├── index.html                     # 主页
    │   ├── user.html                      # 用户页面
    │   └── admin.html                     # 管理员页面
    └── ...
```

## 使用方法

### 方法1：运行打包后的应用（推荐）
1. 进入 `dist-packager/license-management-desktop-win32-x64/` 目录
2. 双击 `license-management-desktop.exe` 启动应用
3. 应用会自动启动后端服务器并打开桌面窗口

### 方法2：开发模式运行
1. 双击根目录的 `start-desktop.bat` 文件
2. 或在命令行中运行 `npm start`

## 功能特性

### 桌面应用特性
- **独立运行**：无需手动启动浏览器，直接运行桌面应用
- **自动服务器管理**：应用启动时自动启动后端服务器，关闭时自动停止
- **原生菜单**：提供文件菜单，可快速切换到不同页面
- **窗口管理**：支持缩放、全屏等标准桌面应用功能

### 页面导航
应用提供以下页面访问：
- **主页**：系统介绍和导航页面
- **用户中心**：用户登录、许可证查看等功能
- **管理员页面**：用户管理、许可证管理等管理功能

### 菜单功能
- **文件菜单**：
  - 主页：跳转到系统主页
  - 用户中心：跳转到用户功能页面
  - 管理员页面：跳转到管理功能页面
  - 退出：关闭应用
- **查看菜单**：
  - 刷新、强制刷新
  - 开发者工具（调试用）
  - 缩放控制
  - 全屏切换
- **帮助菜单**：
  - 关于：显示应用信息

## 技术架构

### 前端
- **Electron**：桌面应用框架
- **HTML/CSS/JavaScript**：用户界面
- **响应式设计**：适配不同屏幕尺寸

### 后端
- **Node.js**：服务器运行时
- **Express**：Web 框架
- **SQLite**：数据库
- **打包为 exe**：使用 pkg 工具打包

### 通信方式
- 桌面应用通过 HTTP 请求与后端服务器通信
- 后端服务器运行在 localhost:3000
- 前端页面通过 AJAX 调用后端 API

## 部署说明

### 完整部署包
将以下文件和目录复制到目标机器：
```
dist-packager/license-management-desktop-win32-x64/
├── license-management-desktop.exe
├── resources/
└── 所有其他文件
```

### 系统要求
- **操作系统**：Windows 7 及以上版本
- **架构**：x64 (64位)
- **内存**：建议 4GB 以上
- **磁盘空间**：约 200MB

### 首次运行
1. 确保目标机器满足系统要求
2. 运行 `license-management-desktop.exe`
3. Windows 可能会显示安全警告，选择"仍要运行"
4. 应用会自动创建数据库和必要的配置文件

## 开发说明

### 重新打包
如果需要重新打包应用：

```bash
# 安装依赖
npm install

# 构建后端 exe（如果有更新）
cd backend
npm run build
npm run pkg
cd ..

# 打包桌面应用
npm run pack
```

### 开发模式
```bash
# 开发模式运行（不打包）
npm run dev
```

### 自定义配置
- 修改 `main.js` 可以调整窗口大小、菜单等
- 修改 `package.json` 中的 build 配置可以调整打包选项
- 后端配置在 `backend/` 目录中

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查是否有杀毒软件阻止
   - 确保有足够的磁盘空间
   - 尝试以管理员身份运行

2. **页面无法加载**
   - 检查端口 3000 是否被占用
   - 等待几秒钟让后端服务器完全启动
   - 使用菜单中的"刷新"功能

3. **数据库错误**
   - 确保应用有写入权限
   - 检查 `backend/data/` 目录是否存在

4. **性能问题**
   - 关闭不必要的其他程序
   - 确保系统满足最低要求

### 日志查看
- 开发者工具：菜单 -> 查看 -> 开发者工具
- 控制台中可以查看详细的错误信息

## 更新说明

当有新版本时：
1. 备份现有的数据库文件（`backend/data/`）
2. 替换整个应用目录
3. 恢复数据库文件
4. 重新启动应用

---

**版本**：1.0.0  
**更新日期**：2025年9月2日  
**技术支持**：License Management System Team
