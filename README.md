# 软件权限管理系统

一个完整的软件权限管理解决方案，包含Node.js后端API和Unity SDK，支持在线和离线许可证激活。

## 系统架构

```
License_Management/
├── backend/                    # Node.js 后端服务
│   ├── src/
│   │   ├── controllers/        # 控制器层
│   │   ├── models/            # 数据模型 (Sequelize)
│   │   ├── services/          # 业务逻辑层
│   │   ├── routes/            # 路由定义
│   │   ├── middleware/        # 中间件
│   │   └── utils/             # 工具函数
│   ├── package.json
│   └── tsconfig.json
├── unity-package/              # Unity 权限验证 SDK
│   ├── Runtime/               # 运行时脚本
│   │   ├── LicenseManager.cs  # 核心管理器
│   │   ├── OnlineActivator.cs # 在线激活
│   │   └── OfflineActivator.cs # 离线激活
│   └── Editor/                # 编辑器工具
└── shared/                    # 共享代码
    ├── interfaces/           # 接口定义
    ├── enums/               # 枚举定义
    └── utils/               # 工具函数
```

## 功能特性

### 🎯 用户前端系统
- ✅ **用户注册和登录** - 安全的用户账户管理
- ✅ **软件产品展示** - 浏览所有可用的软件产品
- ✅ **权限状态显示** - 实时显示用户对各软件的授权状态
- ✅ **试用申请功能** - 一键申请软件试用许可证
- ✅ **离线激活文件下载** - 下载激活文件用于离线激活
- ✅ **个人许可证管理** - 查看和管理个人拥有的所有许可证
- ✅ **购买功能接口** - 预留的购买许可证功能

### 🛠️ 后端管理系统
- ✅ 用户认证和授权 (JWT)
- ✅ 许可证在线激活
- ✅ 许可证验证
- ✅ 许可证管理 (撤销、查询)
- ✅ 安全加密和哈希
- ✅ 速率限制和安全防护
- ✅ RESTful API 设计
- ✅ **完整的后台管理系统**
- ✅ **用户管理 (增删改查)**
- ✅ **软件管理 (增删改查)**
- ✅ **许可证管理 (生成、撤销)**
- ✅ **数据统计和仪表板**
- ✅ **操作日志记录**

### Unity SDK
- ✅ 在线许可证激活
- ✅ 离线许可证支持
- ✅ 自动许可证验证
- ✅ 机器ID绑定
- ✅ 编辑器工具界面
- ✅ 异步操作支持

## 🚀 快速开始

### 📖 系统概述

这是一个完整的软件权限管理解决方案，包含：
- **🖥️ Web后台管理系统** - 用户、软件、许可证的可视化管理
- **🔌 RESTful API** - 用于客户端集成
- **🎮 Unity SDK** - 游戏和应用的权限验证

### 1. 后端部署

#### 环境要求
- Node.js 16+ 
- npm 或 yarn

#### 安装步骤

```bash
# 1. 进入后端目录
cd backend

# 2. 安装依赖
npm install

# 3. 复制环境配置
cp config.example.env .env

# 4. 编辑环境配置
# 修改 .env 文件中的配置项

# 5. 构建项目
npm run build

# 6. 启动服务
npm start

# 开发模式启动
npm run dev
```

#### 环境配置

编辑 `.env` 文件：

```env
# 服务器配置
PORT=3000
NODE_ENV=production

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 数据库配置
DB_PATH=./database/license_management.db

# 加密配置
ENCRYPTION_KEY=your-32-character-encryption-key!!

# 安全配置
BCRYPT_ROUNDS=12
```

### 2. Unity SDK 集成

#### 导入SDK

1. 将 `unity-package` 文件夹复制到Unity项目的 `Assets` 目录下
2. 或者创建 Unity Package 进行导入

#### 基本使用

```csharp
using LicenseManagement;

public class GameManager : MonoBehaviour
{
    private LicenseManager licenseManager;

    void Start()
    {
        // 获取许可证管理器
        licenseManager = FindObjectOfType<LicenseManager>();
        
        // 初始化
        licenseManager.Initialize(
            LicenseType.Online,
            "http://localhost:3000",
            "ABCD-EFGH-IJKL-MNOP"
        );

        // 订阅事件
        licenseManager.OnLicenseValidated += OnLicenseValidated;
        licenseManager.OnLicenseActivated += OnLicenseActivated;
    }

    async void ActivateLicense()
    {
        bool success = await licenseManager.ActivateSoftwareAsync();
        if (success)
        {
            Debug.Log("许可证激活成功！");
        }
    }

    private void OnLicenseValidated(bool isValid)
    {
        if (!isValid)
        {
            // 显示激活界面或退出应用
            ShowActivationDialog();
        }
    }

    private void OnLicenseActivated(ActivationResult result)
    {
        if (result.Success)
        {
            Debug.Log($"激活成功: {result.Message}");
        }
        else
        {
            Debug.LogError($"激活失败: {result.Message}");
        }
    }
}
```

#### 编辑器工具

1. **创建许可证管理器**: `Tools > License Management > Create License Manager`
2. **打开数据文件夹**: `Tools > License Management > Open License Data Folder`
3. **清除许可证数据**: `Tools > License Management > Clear License Data`

## API 接口文档

### 认证接口

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "roles": ["user"]
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 许可证接口

#### 激活许可证
```http
POST /api/licenses/activate
Content-Type: application/json

{
  "productKey": "ABCD-EFGH-IJKL-MNOP",
  "machineId": "unique-machine-id",
  "activationType": "online"
}
```

#### 验证许可证
```http
POST /api/licenses/validate/{licenseKey}
Content-Type: application/json

{
  "machineId": "unique-machine-id"
}
```

#### 获取许可证信息
```http
GET /api/licenses/{licenseKey}
Authorization: Bearer {jwt-token}
```

#### 撤销许可证
```http
DELETE /api/licenses/{licenseKey}/revoke
Authorization: Bearer {jwt-token}
```

### 🎯 用户API接口

#### 用户注册和登录
```http
POST /api/user/register       # 用户注册
POST /api/user/login          # 用户登录
```

#### 软件和许可证
```http
GET /api/user/software        # 获取软件列表
GET /api/user/licenses        # 获取个人许可证
POST /api/user/trial          # 申请试用许可证
GET /api/user/activation-file/{licenseKey}   # 下载激活文件
```

#### 许可证验证
```http
POST /api/user/verify-activation    # 验证激活文件（供软件客户端使用）
```

### 🛠️ 管理API接口

#### 获取统计数据
```http
GET /api/admin/stats
Authorization: Bearer {jwt-token}
```

#### 用户管理
```http
GET /api/admin/users          # 获取用户列表
POST /api/admin/users         # 创建用户
DELETE /api/admin/users/{id}  # 删除用户
```

#### 软件管理
```http
GET /api/admin/software          # 获取软件列表  
POST /api/admin/software         # 创建软件
DELETE /api/admin/software/{id}  # 删除软件
```

#### 许可证管理
```http
GET /api/admin/licenses                      # 获取许可证列表
POST /api/admin/licenses/generate            # 生成许可证
POST /api/admin/licenses/{key}/revoke        # 撤销许可证
```

#### 操作日志
```http
GET /api/admin/logs           # 获取操作日志
```

## 测试步骤

### 1. 启动后端服务

```bash
cd backend
npm run dev
```

服务启动后访问: http://localhost:3000

### 2. 使用系统

服务启动后，可以访问不同的功能：

#### 🏠 系统首页
访问：**http://localhost:3000**
- 选择进入用户中心或管理后台

#### 👥 用户前端系统  
访问：**http://localhost:3000/user**

**新用户注册**：
1. 点击"注册"标签
2. 填写用户名、邮箱、密码
3. 完成注册后使用账户登录

**用户功能**：
- 浏览所有软件产品
- 查看个人授权状态
- 申请软件试用（自动生成试用许可证）
- 下载离线激活文件
- 管理个人许可证

#### 🛠️ 管理后台系统
访问：**http://localhost:3000/admin**

**管理员登录**：
- **用户名**: `admin`
- **密码**: `admin123`

#### 管理功能
1. **仪表板**: 查看系统统计数据和最近活动
2. **用户管理**: 
   - 查看用户列表
   - 添加新用户
   - 删除用户
   - 用户角色管理 (admin/manager/user)
3. **软件管理**:
   - 查看软件产品列表
   - 添加新软件产品
   - 生成产品密钥
   - 启用/禁用软件
4. **许可证管理**:
   - 查看所有许可证
   - 生成新许可证
   - 撤销许可证
   - 许可证状态管理
5. **操作日志**: 查看所有系统操作记录

### 3. 测试API（可选）

#### 用户登录 (默认管理员账户)
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```


#### 激活许可证
```bash
curl -X POST http://localhost:3000/api/licenses/activate \
  -H "Content-Type: application/json" \
  -d '{
    "productKey": "ABCD-EFGH-IJKL-MNOP",
    "machineId": "test-machine-001",
    "activationType": "online"
  }'
```

### 4. 测试Unity SDK

1. 在Unity中创建空场景
2. 使用菜单 `Tools > License Management > Create License Manager`
3. 在Inspector中配置参数:
   - API端点: `http://localhost:3000`
   - 产品密钥: `ABCD-EFGH-IJKL-MNOP`
4. 播放场景并点击"激活许可证"按钮

## 部署指南

### 生产环境部署

#### 后端部署 (Docker)

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/app.js"]
```

#### 环境变量配置

```bash
# 生产环境必须设置的变量
JWT_SECRET=生产环境的强密钥
ENCRYPTION_KEY=32位加密密钥
NODE_ENV=production
PORT=3000
```

### Unity 发布配置

#### 在线模式
- 确保API端点指向生产服务器
- 配置正确的产品密钥
- 建议加密保存许可证数据

#### 离线模式
- 预先生成离线许可证文件
- 使用强加密保护许可证文件
- 实现防篡改机制

## 安全建议

### 后端安全
1. **使用HTTPS**: 生产环境必须使用SSL/TLS
2. **强密钥**: JWT和加密密钥必须足够复杂
3. **速率限制**: API调用频率限制
4. **输入验证**: 严格验证所有输入数据
5. **日志监控**: 记录所有重要操作

### 客户端安全
1. **代码混淆**: Unity代码混淆保护
2. **反调试**: 实现反调试和防篡改
3. **网络安全**: HTTPS通信，证书验证
4. **本地加密**: 许可证数据加密存储
5. **完整性校验**: 验证应用程序完整性

## 故障排除

### 常见问题

#### 1. API连接失败
- 检查网络连接和防火墙设置
- 确认API端点地址正确
- 检查CORS配置

#### 2. 许可证激活失败
- 验证产品密钥格式正确
- 检查机器ID生成是否正常
- 确认服务器端许可证配置

#### 3. Unity编译错误
- 确保所有依赖的命名空间已引入
- 检查.NET版本兼容性
- 更新Unity到支持的版本

### 调试模式

开启调试模式可以查看详细日志：

```csharp
// Unity中开启调试
licenseManager.debugMode = true;
```

```bash
# 后端开启调试
DEBUG=license:* npm run dev
```

## 许可证

MIT License - 详见 LICENSE 文件

## 技术支持

如有问题请提交 Issue 或联系技术支持团队。

---

**注意**: 这是一个基础实现，生产环境使用时请根据具体需求进行安全加固和功能扩展。 