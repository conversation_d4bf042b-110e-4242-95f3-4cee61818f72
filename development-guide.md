# 软件权限系统开发方案

## 系统架构
├── unity-package/                 # Unity 权限验证 SDK
│   ├── Runtime/
│   │   ├── LicenseManager.cs      # 权限管理核心类
│   │   ├── OnlineActivator.cs     # 在线激活实现
│   │   ├── OfflineActivator.cs    # 离线激活实现
│   │   └── LicenseData.cs         # 权限数据模型
│   └── Editor/
│       └── LicenseConfigEditor.cs # 编辑器配置界面
│
├── backend/                       # Node.js 后端服务
│   ├── src/
│   │   ├── app.js                 # 应用入口
│   │   ├── config/                # 配置文件
│   │   ├── controllers/           # 控制器层
│   │   ├── models/                # 数据模型
│   │   ├── routes/                # 路由定义
│   │   ├── services/              # 业务逻辑
│   │   └── utils/                 # 工具函数
│   ├── database/                  # 数据库相关
│   │   ├── migrations/            # 数据库迁移
│   │   └── seeders/               # 数据填充
│   └── public/                    # 静态资源
│
└── shared/                        # 前后端共享代码
    ├── enums/                     # 枚举定义
    ├── interfaces/                # 接口定义
    └── utils/                     # 工具函数
## 后端实现 (Node.js + SQLite)

### 技术栈
- 框架: Express.js
- 数据库: SQLite + Sequelize ORM
- 认证: JWT + bcrypt
- 开发工具: TypeScript, ESLint, Prettier

### 核心功能实现

#### 用户管理// models/User.ts
import { Model, Table, Column, DataType } from 'sequelize-typescript';

@Table
export class User extends Model<User> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  username: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  passwordHash: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    defaultValue: [],
  })
  roles: string[];
}
#### 软件管理// models/Software.ts
@Table
export class Software extends Model<Software> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  productKey: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  isActive: boolean;
}
#### 权限管理// models/License.ts
@Table
export class License extends Model<License> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  licenseKey: string;

  @Column({
    type: DataType.ENUM('online', 'offline'),
    allowNull: false,
  })
  activationType: 'online' | 'offline';

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  activatedAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expiresAt: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  machineId: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isRevoked: boolean;

  // 关联关系
  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Software)
  software: Software;
}
## Unity 前端实现 (C#)

### 核心功能实现

#### 权限管理器// Runtime/LicenseManager.cs
public class LicenseManager : MonoBehaviour
{
    private const string LicenseDataPath = "license_data.json";
    private LicenseData licenseData;
    private IActivator currentActivator;

    // 初始化权限管理器
    public void Initialize(LicenseType licenseType, string apiEndpoint)
    {
        // 根据授权类型选择激活器
        currentActivator = licenseType switch
        {
            LicenseType.Online => new OnlineActivator(apiEndpoint),
            LicenseType.Offline => new OfflineActivator(apiEndpoint),
            _ => throw new ArgumentException("Unsupported license type")
        };

        // 加载已保存的权限数据
        LoadLicenseData();
    }

    // 激活软件
    public async Task<bool> ActivateSoftware(string productKey, string machineId)
    {
        var activationResult = await currentActivator.Activate(productKey, machineId);
        
        if (activationResult.Success)
        {
            licenseData = new LicenseData
            {
                LicenseKey = activationResult.LicenseKey,
                ActivationType = activationResult.ActivationType,
                ExpiresAt = activationResult.ExpiresAt,
                IsActivated = true
            };
            
            SaveLicenseData();
        }
        
        return activationResult.Success;
    }

    // 验证权限
    public async Task<bool> ValidateLicense()
    {
        if (licenseData == null || !licenseData.IsActivated)
            return false;
            
        return await currentActivator.Validate(licenseData.LicenseKey);
    }
    
    // 其他核心方法...
}
#### 在线激活实现// Runtime/OnlineActivator.cs
public class OnlineActivator : IActivator
{
    private readonly string apiEndpoint;
    private readonly HttpClient httpClient;

    public OnlineActivator(string apiEndpoint)
    {
        this.apiEndpoint = apiEndpoint;
        httpClient = new HttpClient();
    }

    public async Task<ActivationResult> Activate(string productKey, string machineId)
    {
        var requestData = new
        {
            productKey,
            machineId,
            activationType = "online"
        };

        var content = new StringContent(
            JsonConvert.SerializeObject(requestData),
            Encoding.UTF8,
            "application/json");

        var response = await httpClient.PostAsync($"{apiEndpoint}/api/licenses/activate", content);
        
        if (response.IsSuccessStatusCode)
        {
            var json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<ActivationResult>(json);
        }
        
        return new ActivationResult { Success = false };
    }

    public async Task<bool> Validate(string licenseKey)
    {
        var response = await httpClient.GetAsync($"{apiEndpoint}/api/licenses/validate/{licenseKey}");
        return response.IsSuccessStatusCode;
    }
}
## 集成指南

### Unity 集成步骤
1. 将 Unity 包导入到项目中
2. 在游戏初始化时调用 `LicenseManager.Initialize()`
3. 在主菜单添加激活界面
4. 在游戏核心逻辑前验证权限

### 后端部署步骤
1. 安装 Node.js 和 npm
2. 运行 `npm install` 安装依赖
3. 配置环境变量（.env 文件）
4. 运行数据库迁移 `npx sequelize-cli db:migrate`
5. 启动服务 `npm start`

## 安全建议
1. 使用 HTTPS 保证通信安全
2. 实现 License Key 加密存储
3. 定期更新安全证书
4. 实现防篡改机制
5. 定期审计权限使用情况
    