{"name": "moment-timezone", "version": "0.5.48", "description": "Parse and display moments in any timezone.", "homepage": "http://momentjs.com/timezone/", "author": "<PERSON> <<EMAIL>> (http://timwoodcreates.com/)", "keywords": ["moment", "date", "time", "timezone", "olson", "iana", "zone", "tz"], "main": "./index.js", "typings": "./index.d.ts", "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/moment/moment-timezone.git"}, "bugs": {"url": "https://github.com/moment/moment-timezone/issues"}, "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "devDependencies": {"grunt": "^1.5.3", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-nodeunit": "^5.0.0", "grunt-contrib-uglify": "^5.2.2", "grunt-exec": "^3.0.0", "typescript": "^3.5.1"}, "jspm": {"main": "builds/moment-timezone-with-data", "shim": {"moment-timezone": {"deps": ["moment"]}}}, "scripts": {"test": "grunt"}}