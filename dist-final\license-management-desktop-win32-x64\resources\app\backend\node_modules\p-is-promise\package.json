{"name": "p-is-promise", "version": "3.0.0", "description": "Check if something is a promise", "license": "MIT", "repository": "sindresorhus/p-is-promise", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "is", "detect", "check", "kind", "type", "thenable", "es2015", "async", "await", "promises", "bluebird"], "devDependencies": {"ava": "^2.1.0", "bluebird": "^3.5.4", "tsd": "^0.7.2", "xo": "^0.24.0"}, "xo": {"rules": {"promise/prefer-await-to-then": "off"}}}