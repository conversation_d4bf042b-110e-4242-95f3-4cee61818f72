const { app, BrowserWindow, Menu, shell, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

let mainWindow;
let serverProcess;
const isDev = process.argv.includes('--dev');

// 服务器配置
const SERVER_PORT = 3000;
const SERVER_URL = `http://localhost:${SERVER_PORT}`;

function createWindow() {
    // 创建主窗口
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            webSecurity: true
        },
        icon: path.join(__dirname, 'icon.ico'), // 如果有图标文件
        title: '软件权限管理系统',
        show: false // 先不显示，等服务器启动后再显示
    });

    // 创建菜单
    createMenu();

    // 等待服务器启动后加载页面
    setTimeout(() => {
        mainWindow.loadURL(SERVER_URL);
        mainWindow.show();
    }, 3000);

    // 处理窗口关闭
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

function createMenu() {
    const template = [
        {
            label: '文件',
            submenu: [
                {
                    label: '主页',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.loadURL(SERVER_URL);
                        }
                    }
                },
                {
                    label: '用户中心',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.loadURL(`${SERVER_URL}/user.html`);
                        }
                    }
                },
                {
                    label: '管理员页面',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.loadURL(`${SERVER_URL}/admin.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: '退出',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: '查看',
            submenu: [
                { role: 'reload', label: '刷新' },
                { role: 'forceReload', label: '强制刷新' },
                { role: 'toggleDevTools', label: '开发者工具' },
                { type: 'separator' },
                { role: 'resetZoom', label: '重置缩放' },
                { role: 'zoomIn', label: '放大' },
                { role: 'zoomOut', label: '缩小' },
                { type: 'separator' },
                { role: 'togglefullscreen', label: '全屏' }
            ]
        },
        {
            label: '帮助',
            submenu: [
                {
                    label: '关于',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: '关于',
                            message: '软件权限管理系统',
                            detail: '版本 1.0.0\n\n一个用于管理软件许可证和用户权限的桌面应用程序。'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

function startServer() {
    return new Promise((resolve, reject) => {
        const exePath = path.join(__dirname, 'backend', 'license-management.exe');
        const batPath = path.join(__dirname, 'backend', 'start-server.bat');
        
        // 检查 exe 文件是否存在
        if (fs.existsSync(exePath)) {
            console.log('启动后端服务器 (EXE)...');
            serverProcess = spawn(exePath, [], {
                cwd: path.join(__dirname, 'backend'),
                stdio: 'pipe'
            });
        } else if (isDev) {
            // 开发模式下使用 npm run dev
            console.log('启动后端服务器 (开发模式)...');
            serverProcess = spawn('npm', ['run', 'dev'], {
                cwd: path.join(__dirname, 'backend'),
                stdio: 'pipe',
                shell: true
            });
        } else {
            reject(new Error('未找到后端服务器文件'));
            return;
        }

        serverProcess.stdout.on('data', (data) => {
            console.log(`服务器输出: ${data}`);
        });

        serverProcess.stderr.on('data', (data) => {
            console.error(`服务器错误: ${data}`);
        });

        serverProcess.on('close', (code) => {
            console.log(`服务器进程退出，代码: ${code}`);
        });

        // 等待服务器启动
        setTimeout(() => {
            resolve();
        }, 2000);
    });
}

function stopServer() {
    if (serverProcess) {
        console.log('停止后端服务器...');
        serverProcess.kill();
        serverProcess = null;
    }
}

// 应用程序事件处理
app.whenReady().then(async () => {
    try {
        await startServer();
        createWindow();
    } catch (error) {
        console.error('启动服务器失败:', error);
        dialog.showErrorBox('启动错误', '无法启动后端服务器，请检查配置。');
        app.quit();
    }
});

app.on('window-all-closed', () => {
    stopServer();
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

app.on('before-quit', () => {
    stopServer();
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
});
