{"name": "@babel/generator", "version": "7.18.2", "description": "Turns an AST into code.", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "main": "./lib/index.js", "files": ["lib"], "dependencies": {"@babel/types": "^7.18.2", "@jridgewell/gen-mapping": "^0.3.0", "jsesc": "^2.5.1"}, "devDependencies": {"@babel/helper-fixtures": "^7.17.10", "@babel/parser": "^7.18.0", "@jridgewell/trace-mapping": "^0.3.8", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}}