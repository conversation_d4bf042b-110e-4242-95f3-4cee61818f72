{"version": 3, "names": ["assertNode", "node", "isNode", "type", "JSON", "stringify", "TypeError"], "sources": ["../../src/asserts/assertNode.ts"], "sourcesContent": ["import isNode from \"../validators/isNode\";\nimport type * as t from \"..\";\n\nexport default function assertNode(node?: any): asserts node is t.Node {\n  if (!isNode(node)) {\n    const type = node?.type ?? JSON.stringify(node);\n    throw new TypeError(`Not a valid node of type \"${type as any}\"`);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,UAAT,CAAoBC,IAApB,EAAwD;EACrE,IAAI,CAAC,IAAAC,eAAA,EAAOD,IAAP,CAAL,EAAmB;IAAA;;IACjB,MAAME,IAAI,iBAAGF,IAAH,oBAAGA,IAAI,CAAEE,IAAT,yBAAiBC,IAAI,CAACC,SAAL,CAAeJ,IAAf,CAA3B;IACA,MAAM,IAAIK,SAAJ,CAAe,6BAA4BH,IAAY,GAAvD,CAAN;EACD;AACF"}