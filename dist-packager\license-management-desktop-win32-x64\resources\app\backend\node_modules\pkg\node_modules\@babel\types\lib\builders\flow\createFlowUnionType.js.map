{"version": 3, "names": ["createFlowUnionType", "types", "flattened", "removeTypeDuplicates", "length", "unionTypeAnnotation"], "sources": ["../../../src/builders/flow/createFlowUnionType.ts"], "sourcesContent": ["import { unionTypeAnnotation } from \"../generated\";\nimport removeTypeDuplicates from \"../../modifications/flow/removeTypeDuplicates\";\nimport type * as t from \"../..\";\n\n/**\n * Takes an array of `types` and flattens them, removing duplicates and\n * returns a `UnionTypeAnnotation` node containing them.\n */\nexport default function createFlowUnionType<T extends t.FlowType>(\n  types: [T] | Array<T>,\n): T | t.UnionTypeAnnotation {\n  const flattened = removeTypeDuplicates(types);\n\n  if (flattened.length === 1) {\n    return flattened[0] as T;\n  } else {\n    return unionTypeAnnotation(flattened);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAOe,SAASA,mBAAT,CACbC,KADa,EAEc;EAC3B,MAAMC,SAAS,GAAG,IAAAC,6BAAA,EAAqBF,KAArB,CAAlB;;EAEA,IAAIC,SAAS,CAACE,MAAV,KAAqB,CAAzB,EAA4B;IAC1B,OAAOF,SAAS,CAAC,CAAD,CAAhB;EACD,CAFD,MAEO;IACL,OAAO,IAAAG,8BAAA,EAAoBH,SAApB,CAAP;EACD;AACF"}