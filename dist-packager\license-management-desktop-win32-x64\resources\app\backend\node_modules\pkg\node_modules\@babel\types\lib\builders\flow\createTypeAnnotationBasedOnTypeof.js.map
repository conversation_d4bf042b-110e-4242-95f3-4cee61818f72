{"version": 3, "names": ["createTypeAnnotationBasedOnTypeof", "type", "stringTypeAnnotation", "numberTypeAnnotation", "voidTypeAnnotation", "booleanTypeAnnotation", "genericTypeAnnotation", "identifier", "anyTypeAnnotation", "Error"], "sources": ["../../../src/builders/flow/createTypeAnnotationBasedOnTypeof.ts"], "sourcesContent": ["import {\n  anyTypeAnnotation,\n  stringTypeAnnotation,\n  numberTypeAnnotation,\n  voidTypeAnnotation,\n  booleanTypeAnnotation,\n  genericTypeAnnotation,\n  identifier,\n} from \"../generated\";\nimport type * as t from \"../..\";\n\nexport default createTypeAnnotationBasedOnTypeof as {\n  (type: \"string\"): t.StringTypeAnnotation;\n  (type: \"number\"): t.NumberTypeAnnotation;\n  (type: \"undefined\"): t.VoidTypeAnnotation;\n  (type: \"boolean\"): t.<PERSON>anTypeAnnotation;\n  (type: \"function\"): t.GenericTypeAnnotation;\n  (type: \"object\"): t.GenericTypeAnnotation;\n  (type: \"symbol\"): t.GenericTypeAnnotation;\n  (type: \"bigint\"): t.AnyTypeAnnotation;\n};\n\n/**\n * Create a type annotation based on typeof expression.\n */\nfunction createTypeAnnotationBasedOnTypeof(type: string): t.FlowType {\n  switch (type) {\n    case \"string\":\n      return stringTypeAnnotation();\n    case \"number\":\n      return numberTypeAnnotation();\n    case \"undefined\":\n      return voidTypeAnnotation();\n    case \"boolean\":\n      return booleanTypeAnnotation();\n    case \"function\":\n      return genericTypeAnnotation(identifier(\"Function\"));\n    case \"object\":\n      return genericTypeAnnotation(identifier(\"Object\"));\n    case \"symbol\":\n      return genericTypeAnnotation(identifier(\"Symbol\"));\n    case \"bigint\":\n      // todo: use BigInt annotation when Flow supports BigInt\n      // https://github.com/facebook/flow/issues/6639\n      return anyTypeAnnotation();\n  }\n  throw new Error(\"Invalid typeof value: \" + type);\n}\n"], "mappings": ";;;;;;;AAAA;;eAWeA,iC;;;AAcf,SAASA,iCAAT,CAA2CC,IAA3C,EAAqE;EACnE,QAAQA,IAAR;IACE,KAAK,QAAL;MACE,OAAO,IAAAC,+BAAA,GAAP;;IACF,KAAK,QAAL;MACE,OAAO,IAAAC,+BAAA,GAAP;;IACF,KAAK,WAAL;MACE,OAAO,IAAAC,6BAAA,GAAP;;IACF,KAAK,SAAL;MACE,OAAO,IAAAC,gCAAA,GAAP;;IACF,KAAK,UAAL;MACE,OAAO,IAAAC,gCAAA,EAAsB,IAAAC,qBAAA,EAAW,UAAX,CAAtB,CAAP;;IACF,KAAK,QAAL;MACE,OAAO,IAAAD,gCAAA,EAAsB,IAAAC,qBAAA,EAAW,QAAX,CAAtB,CAAP;;IACF,KAAK,QAAL;MACE,OAAO,IAAAD,gCAAA,EAAsB,IAAAC,qBAAA,EAAW,QAAX,CAAtB,CAAP;;IACF,KAAK,QAAL;MAGE,OAAO,IAAAC,4BAAA,GAAP;EAlBJ;;EAoBA,MAAM,IAAIC,KAAJ,CAAU,2BAA2BR,IAArC,CAAN;AACD"}