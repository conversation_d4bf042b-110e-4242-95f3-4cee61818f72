{"version": 3, "names": ["arrayExpression", "elements", "validateNode", "type", "assignmentExpression", "operator", "left", "right", "binaryExpression", "interpreterDirective", "value", "directive", "directiveLiteral", "blockStatement", "body", "directives", "breakStatement", "label", "callExpression", "callee", "_arguments", "arguments", "catch<PERSON><PERSON><PERSON>", "param", "conditionalExpression", "test", "consequent", "alternate", "continueStatement", "debuggerStatement", "doWhileStatement", "emptyStatement", "expressionStatement", "expression", "file", "program", "comments", "tokens", "forInStatement", "forStatement", "init", "update", "functionDeclaration", "id", "params", "generator", "async", "functionExpression", "identifier", "name", "ifStatement", "labeledStatement", "stringLiteral", "numericLiteral", "nullLiteral", "booleanLiteral", "regExpLiteral", "pattern", "flags", "logicalExpression", "memberExpression", "object", "property", "computed", "optional", "newExpression", "sourceType", "interpreter", "sourceFile", "objectExpression", "properties", "objectMethod", "kind", "key", "objectProperty", "shorthand", "decorators", "restElement", "argument", "returnStatement", "sequenceExpression", "expressions", "parenthesizedExpression", "switchCase", "switchStatement", "discriminant", "cases", "thisExpression", "throwStatement", "tryStatement", "block", "handler", "finalizer", "unaryExpression", "prefix", "updateExpression", "variableDeclaration", "declarations", "variableDeclarator", "whileStatement", "withStatement", "assignmentPattern", "arrayPattern", "arrowFunctionExpression", "classBody", "classExpression", "superClass", "classDeclaration", "exportAllDeclaration", "source", "exportDefaultDeclaration", "declaration", "exportNamedDeclaration", "specifiers", "exportSpecifier", "local", "exported", "forOfStatement", "_await", "await", "importDeclaration", "importDefaultSpecifier", "importNamespaceSpecifier", "importSpecifier", "imported", "metaProperty", "meta", "classMethod", "_static", "static", "objectPattern", "spreadElement", "_super", "taggedTemplateExpression", "tag", "quasi", "templateElement", "tail", "templateLiteral", "quasis", "yieldExpression", "delegate", "awaitExpression", "_import", "bigIntLiteral", "exportNamespaceSpecifier", "optionalMemberExpression", "optionalCallExpression", "classProperty", "typeAnnotation", "classAccessorProperty", "classPrivateProperty", "classPrivateMethod", "privateName", "staticBlock", "anyTypeAnnotation", "arrayTypeAnnotation", "elementType", "booleanTypeAnnotation", "booleanLiteralTypeAnnotation", "nullLiteralTypeAnnotation", "classImplements", "typeParameters", "declareClass", "_extends", "extends", "declareFunction", "declareInterface", "declareModule", "declareModuleExports", "declareTypeAlias", "declareOpaqueType", "supertype", "declareVariable", "declareExportDeclaration", "declareExportAllDeclaration", "declaredPredicate", "existsTypeAnnotation", "functionTypeAnnotation", "rest", "returnType", "functionTypeParam", "genericTypeAnnotation", "inferredPredicate", "interfaceExtends", "interfaceDeclaration", "interfaceTypeAnnotation", "intersectionTypeAnnotation", "types", "mixedTypeAnnotation", "emptyTypeAnnotation", "nullableTypeAnnotation", "numberLiteralTypeAnnotation", "numberTypeAnnotation", "objectTypeAnnotation", "indexers", "callProperties", "internalSlots", "exact", "objectTypeInternalSlot", "method", "objectTypeCallProperty", "objectTypeIndexer", "variance", "objectTypeProperty", "proto", "objectTypeSpreadProperty", "opaqueType", "impltype", "qualifiedTypeIdentifier", "qualification", "stringLiteralTypeAnnotation", "stringTypeAnnotation", "symbolTypeAnnotation", "thisTypeAnnotation", "tupleTypeAnnotation", "typeofTypeAnnotation", "typeAlias", "typeCastExpression", "typeParameter", "bound", "_default", "default", "typeParameterDeclaration", "typeParameterInstantiation", "unionTypeAnnotation", "voidTypeAnnotation", "enumDeclaration", "enumBooleanBody", "members", "explicitType", "hasUnknownMembers", "enumNumberBody", "enumStringBody", "enumSymbolBody", "enumBooleanMember", "enumNumberMember", "enumStringMember", "enumDefaultedMember", "indexedAccessType", "objectType", "indexType", "optionalIndexedAccessType", "jsxAttribute", "jsxClosingElement", "jsxElement", "openingElement", "closingElement", "children", "selfClosing", "jsxEmptyExpression", "jsxExpressionContainer", "jsxSpreadChild", "jsxIdentifier", "jsxMemberExpression", "jsxNamespacedName", "namespace", "jsxOpeningElement", "attributes", "jsxSpreadAttribute", "jsxText", "jsxFragment", "openingFragment", "closingFragment", "jsxOpeningFragment", "jsxClosingFragment", "noop", "placeholder", "expectedNode", "v8IntrinsicIdentifier", "argumentPlaceholder", "bindExpression", "importAttribute", "decorator", "doExpression", "exportDefaultSpecifier", "recordExpression", "tupleExpression", "decimalLiteral", "moduleExpression", "topicReference", "pipelineTopicExpression", "pipelineBareFunction", "pipelinePrimaryTopicReference", "tsParameterProperty", "parameter", "tsDeclareFunction", "tsDeclareMethod", "tsQualifiedName", "tsCallSignatureDeclaration", "parameters", "tsConstructSignatureDeclaration", "tsPropertySignature", "initializer", "tsMethodSignature", "tsIndexSignature", "tsAnyKeyword", "tsBooleanKeyword", "tsBigIntKeyword", "tsIntrinsicKeyword", "tsNeverKeyword", "tsNullKeyword", "tsNumberKeyword", "tsObjectKeyword", "tsStringKeyword", "tsSymbolKeyword", "tsUndefinedKeyword", "tsUnknownKeyword", "tsVoidKeyword", "tsThisType", "tsFunctionType", "tsConstructorType", "tsTypeReference", "typeName", "tsTypePredicate", "parameterName", "asserts", "tsTypeQuery", "exprName", "tsType<PERSON><PERSON>al", "tsArrayType", "tsTupleType", "elementTypes", "tsOptionalType", "tsRestType", "tsNamedTupleMember", "tsUnionType", "tsIntersectionType", "tsConditionalType", "checkType", "extendsType", "trueType", "falseType", "tsInferType", "tsParenthesizedType", "tsTypeOperator", "tsIndexedAccessType", "tsMappedType", "nameType", "tsLiteralType", "literal", "tsExpressionWithTypeArguments", "tsInterfaceDeclaration", "tsInterfaceBody", "tsTypeAliasDeclaration", "tsInstantiationExpression", "tsAsExpression", "tsTypeAssertion", "tsEnumDeclaration", "tsEnumMember", "tsModuleDeclaration", "tsModuleBlock", "tsImportType", "qualifier", "tsImportEqualsDeclaration", "moduleReference", "isExport", "tsExternalModuleReference", "tsNonNullExpression", "tsExportAssignment", "tsNamespaceExportDeclaration", "tsTypeAnnotation", "tsTypeParameterInstantiation", "tsTypeParameterDeclaration", "tsTypeParameter", "constraint", "NumberLiteral", "console", "trace", "RegexLiteral", "RestProperty", "SpreadProperty"], "sources": ["../../../src/builders/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport validateNode from \"../validateNode\";\nimport type * as t from \"../..\";\nexport function arrayExpression(\n  elements: Array<null | t.Expression | t.SpreadElement> = [],\n): t.ArrayExpression {\n  return validateNode<t.ArrayExpression>({\n    type: \"ArrayExpression\",\n    elements,\n  });\n}\nexport function assignmentExpression(\n  operator: string,\n  left: t.LVal,\n  right: t.Expression,\n): t.AssignmentExpression {\n  return validateNode<t.AssignmentExpression>({\n    type: \"AssignmentExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function binaryExpression(\n  operator:\n    | \"+\"\n    | \"-\"\n    | \"/\"\n    | \"%\"\n    | \"*\"\n    | \"**\"\n    | \"&\"\n    | \"|\"\n    | \">>\"\n    | \">>>\"\n    | \"<<\"\n    | \"^\"\n    | \"==\"\n    | \"===\"\n    | \"!=\"\n    | \"!==\"\n    | \"in\"\n    | \"instanceof\"\n    | \">\"\n    | \"<\"\n    | \">=\"\n    | \"<=\"\n    | \"|>\",\n  left: t.Expression | t.PrivateName,\n  right: t.Expression,\n): t.BinaryExpression {\n  return validateNode<t.BinaryExpression>({\n    type: \"BinaryExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function interpreterDirective(value: string): t.InterpreterDirective {\n  return validateNode<t.InterpreterDirective>({\n    type: \"InterpreterDirective\",\n    value,\n  });\n}\nexport function directive(value: t.DirectiveLiteral): t.Directive {\n  return validateNode<t.Directive>({\n    type: \"Directive\",\n    value,\n  });\n}\nexport function directiveLiteral(value: string): t.DirectiveLiteral {\n  return validateNode<t.DirectiveLiteral>({\n    type: \"DirectiveLiteral\",\n    value,\n  });\n}\nexport function blockStatement(\n  body: Array<t.Statement>,\n  directives: Array<t.Directive> = [],\n): t.BlockStatement {\n  return validateNode<t.BlockStatement>({\n    type: \"BlockStatement\",\n    body,\n    directives,\n  });\n}\nexport function breakStatement(\n  label: t.Identifier | null = null,\n): t.BreakStatement {\n  return validateNode<t.BreakStatement>({\n    type: \"BreakStatement\",\n    label,\n  });\n}\nexport function callExpression(\n  callee: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n): t.CallExpression {\n  return validateNode<t.CallExpression>({\n    type: \"CallExpression\",\n    callee,\n    arguments: _arguments,\n  });\n}\nexport function catchClause(\n  param:\n    | t.Identifier\n    | t.ArrayPattern\n    | t.ObjectPattern\n    | null\n    | undefined = null,\n  body: t.BlockStatement,\n): t.CatchClause {\n  return validateNode<t.CatchClause>({\n    type: \"CatchClause\",\n    param,\n    body,\n  });\n}\nexport function conditionalExpression(\n  test: t.Expression,\n  consequent: t.Expression,\n  alternate: t.Expression,\n): t.ConditionalExpression {\n  return validateNode<t.ConditionalExpression>({\n    type: \"ConditionalExpression\",\n    test,\n    consequent,\n    alternate,\n  });\n}\nexport function continueStatement(\n  label: t.Identifier | null = null,\n): t.ContinueStatement {\n  return validateNode<t.ContinueStatement>({\n    type: \"ContinueStatement\",\n    label,\n  });\n}\nexport function debuggerStatement(): t.DebuggerStatement {\n  return {\n    type: \"DebuggerStatement\",\n  };\n}\nexport function doWhileStatement(\n  test: t.Expression,\n  body: t.Statement,\n): t.DoWhileStatement {\n  return validateNode<t.DoWhileStatement>({\n    type: \"DoWhileStatement\",\n    test,\n    body,\n  });\n}\nexport function emptyStatement(): t.EmptyStatement {\n  return {\n    type: \"EmptyStatement\",\n  };\n}\nexport function expressionStatement(\n  expression: t.Expression,\n): t.ExpressionStatement {\n  return validateNode<t.ExpressionStatement>({\n    type: \"ExpressionStatement\",\n    expression,\n  });\n}\nexport function file(\n  program: t.Program,\n  comments: Array<t.CommentBlock | t.CommentLine> | null = null,\n  tokens: Array<any> | null = null,\n): t.File {\n  return validateNode<t.File>({\n    type: \"File\",\n    program,\n    comments,\n    tokens,\n  });\n}\nexport function forInStatement(\n  left: t.VariableDeclaration | t.LVal,\n  right: t.Expression,\n  body: t.Statement,\n): t.ForInStatement {\n  return validateNode<t.ForInStatement>({\n    type: \"ForInStatement\",\n    left,\n    right,\n    body,\n  });\n}\nexport function forStatement(\n  init: t.VariableDeclaration | t.Expression | null | undefined = null,\n  test: t.Expression | null | undefined = null,\n  update: t.Expression | null | undefined = null,\n  body: t.Statement,\n): t.ForStatement {\n  return validateNode<t.ForStatement>({\n    type: \"ForStatement\",\n    init,\n    test,\n    update,\n    body,\n  });\n}\nexport function functionDeclaration(\n  id: t.Identifier | null | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  generator: boolean = false,\n  async: boolean = false,\n): t.FunctionDeclaration {\n  return validateNode<t.FunctionDeclaration>({\n    type: \"FunctionDeclaration\",\n    id,\n    params,\n    body,\n    generator,\n    async,\n  });\n}\nexport function functionExpression(\n  id: t.Identifier | null | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  generator: boolean = false,\n  async: boolean = false,\n): t.FunctionExpression {\n  return validateNode<t.FunctionExpression>({\n    type: \"FunctionExpression\",\n    id,\n    params,\n    body,\n    generator,\n    async,\n  });\n}\nexport function identifier(name: string): t.Identifier {\n  return validateNode<t.Identifier>({\n    type: \"Identifier\",\n    name,\n  });\n}\nexport function ifStatement(\n  test: t.Expression,\n  consequent: t.Statement,\n  alternate: t.Statement | null = null,\n): t.IfStatement {\n  return validateNode<t.IfStatement>({\n    type: \"IfStatement\",\n    test,\n    consequent,\n    alternate,\n  });\n}\nexport function labeledStatement(\n  label: t.Identifier,\n  body: t.Statement,\n): t.LabeledStatement {\n  return validateNode<t.LabeledStatement>({\n    type: \"LabeledStatement\",\n    label,\n    body,\n  });\n}\nexport function stringLiteral(value: string): t.StringLiteral {\n  return validateNode<t.StringLiteral>({\n    type: \"StringLiteral\",\n    value,\n  });\n}\nexport function numericLiteral(value: number): t.NumericLiteral {\n  return validateNode<t.NumericLiteral>({\n    type: \"NumericLiteral\",\n    value,\n  });\n}\nexport function nullLiteral(): t.NullLiteral {\n  return {\n    type: \"NullLiteral\",\n  };\n}\nexport function booleanLiteral(value: boolean): t.BooleanLiteral {\n  return validateNode<t.BooleanLiteral>({\n    type: \"BooleanLiteral\",\n    value,\n  });\n}\nexport function regExpLiteral(\n  pattern: string,\n  flags: string = \"\",\n): t.RegExpLiteral {\n  return validateNode<t.RegExpLiteral>({\n    type: \"RegExpLiteral\",\n    pattern,\n    flags,\n  });\n}\nexport function logicalExpression(\n  operator: \"||\" | \"&&\" | \"??\",\n  left: t.Expression,\n  right: t.Expression,\n): t.LogicalExpression {\n  return validateNode<t.LogicalExpression>({\n    type: \"LogicalExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function memberExpression(\n  object: t.Expression | t.Super,\n  property: t.Expression | t.Identifier | t.PrivateName,\n  computed: boolean = false,\n  optional: true | false | null = null,\n): t.MemberExpression {\n  return validateNode<t.MemberExpression>({\n    type: \"MemberExpression\",\n    object,\n    property,\n    computed,\n    optional,\n  });\n}\nexport function newExpression(\n  callee: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n): t.NewExpression {\n  return validateNode<t.NewExpression>({\n    type: \"NewExpression\",\n    callee,\n    arguments: _arguments,\n  });\n}\nexport function program(\n  body: Array<t.Statement>,\n  directives: Array<t.Directive> = [],\n  sourceType: \"script\" | \"module\" = \"script\",\n  interpreter: t.InterpreterDirective | null = null,\n): t.Program {\n  return validateNode<t.Program>({\n    type: \"Program\",\n    body,\n    directives,\n    sourceType,\n    interpreter,\n    sourceFile: null,\n  });\n}\nexport function objectExpression(\n  properties: Array<t.ObjectMethod | t.ObjectProperty | t.SpreadElement>,\n): t.ObjectExpression {\n  return validateNode<t.ObjectExpression>({\n    type: \"ObjectExpression\",\n    properties,\n  });\n}\nexport function objectMethod(\n  kind: \"method\" | \"get\" | \"set\" | undefined = \"method\",\n  key:\n    | t.Expression\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  computed: boolean = false,\n  generator: boolean = false,\n  async: boolean = false,\n): t.ObjectMethod {\n  return validateNode<t.ObjectMethod>({\n    type: \"ObjectMethod\",\n    kind,\n    key,\n    params,\n    body,\n    computed,\n    generator,\n    async,\n  });\n}\nexport function objectProperty(\n  key:\n    | t.Expression\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.DecimalLiteral\n    | t.PrivateName,\n  value: t.Expression | t.PatternLike,\n  computed: boolean = false,\n  shorthand: boolean = false,\n  decorators: Array<t.Decorator> | null = null,\n): t.ObjectProperty {\n  return validateNode<t.ObjectProperty>({\n    type: \"ObjectProperty\",\n    key,\n    value,\n    computed,\n    shorthand,\n    decorators,\n  });\n}\nexport function restElement(argument: t.LVal): t.RestElement {\n  return validateNode<t.RestElement>({\n    type: \"RestElement\",\n    argument,\n  });\n}\nexport function returnStatement(\n  argument: t.Expression | null = null,\n): t.ReturnStatement {\n  return validateNode<t.ReturnStatement>({\n    type: \"ReturnStatement\",\n    argument,\n  });\n}\nexport function sequenceExpression(\n  expressions: Array<t.Expression>,\n): t.SequenceExpression {\n  return validateNode<t.SequenceExpression>({\n    type: \"SequenceExpression\",\n    expressions,\n  });\n}\nexport function parenthesizedExpression(\n  expression: t.Expression,\n): t.ParenthesizedExpression {\n  return validateNode<t.ParenthesizedExpression>({\n    type: \"ParenthesizedExpression\",\n    expression,\n  });\n}\nexport function switchCase(\n  test: t.Expression | null | undefined = null,\n  consequent: Array<t.Statement>,\n): t.SwitchCase {\n  return validateNode<t.SwitchCase>({\n    type: \"SwitchCase\",\n    test,\n    consequent,\n  });\n}\nexport function switchStatement(\n  discriminant: t.Expression,\n  cases: Array<t.SwitchCase>,\n): t.SwitchStatement {\n  return validateNode<t.SwitchStatement>({\n    type: \"SwitchStatement\",\n    discriminant,\n    cases,\n  });\n}\nexport function thisExpression(): t.ThisExpression {\n  return {\n    type: \"ThisExpression\",\n  };\n}\nexport function throwStatement(argument: t.Expression): t.ThrowStatement {\n  return validateNode<t.ThrowStatement>({\n    type: \"ThrowStatement\",\n    argument,\n  });\n}\nexport function tryStatement(\n  block: t.BlockStatement,\n  handler: t.CatchClause | null = null,\n  finalizer: t.BlockStatement | null = null,\n): t.TryStatement {\n  return validateNode<t.TryStatement>({\n    type: \"TryStatement\",\n    block,\n    handler,\n    finalizer,\n  });\n}\nexport function unaryExpression(\n  operator: \"void\" | \"throw\" | \"delete\" | \"!\" | \"+\" | \"-\" | \"~\" | \"typeof\",\n  argument: t.Expression,\n  prefix: boolean = true,\n): t.UnaryExpression {\n  return validateNode<t.UnaryExpression>({\n    type: \"UnaryExpression\",\n    operator,\n    argument,\n    prefix,\n  });\n}\nexport function updateExpression(\n  operator: \"++\" | \"--\",\n  argument: t.Expression,\n  prefix: boolean = false,\n): t.UpdateExpression {\n  return validateNode<t.UpdateExpression>({\n    type: \"UpdateExpression\",\n    operator,\n    argument,\n    prefix,\n  });\n}\nexport function variableDeclaration(\n  kind: \"var\" | \"let\" | \"const\",\n  declarations: Array<t.VariableDeclarator>,\n): t.VariableDeclaration {\n  return validateNode<t.VariableDeclaration>({\n    type: \"VariableDeclaration\",\n    kind,\n    declarations,\n  });\n}\nexport function variableDeclarator(\n  id: t.LVal,\n  init: t.Expression | null = null,\n): t.VariableDeclarator {\n  return validateNode<t.VariableDeclarator>({\n    type: \"VariableDeclarator\",\n    id,\n    init,\n  });\n}\nexport function whileStatement(\n  test: t.Expression,\n  body: t.Statement,\n): t.WhileStatement {\n  return validateNode<t.WhileStatement>({\n    type: \"WhileStatement\",\n    test,\n    body,\n  });\n}\nexport function withStatement(\n  object: t.Expression,\n  body: t.Statement,\n): t.WithStatement {\n  return validateNode<t.WithStatement>({\n    type: \"WithStatement\",\n    object,\n    body,\n  });\n}\nexport function assignmentPattern(\n  left:\n    | t.Identifier\n    | t.ObjectPattern\n    | t.ArrayPattern\n    | t.MemberExpression\n    | t.TSAsExpression\n    | t.TSTypeAssertion\n    | t.TSNonNullExpression,\n  right: t.Expression,\n): t.AssignmentPattern {\n  return validateNode<t.AssignmentPattern>({\n    type: \"AssignmentPattern\",\n    left,\n    right,\n  });\n}\nexport function arrayPattern(\n  elements: Array<null | t.PatternLike | t.LVal>,\n): t.ArrayPattern {\n  return validateNode<t.ArrayPattern>({\n    type: \"ArrayPattern\",\n    elements,\n  });\n}\nexport function arrowFunctionExpression(\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement | t.Expression,\n  async: boolean = false,\n): t.ArrowFunctionExpression {\n  return validateNode<t.ArrowFunctionExpression>({\n    type: \"ArrowFunctionExpression\",\n    params,\n    body,\n    async,\n    expression: null,\n  });\n}\nexport function classBody(\n  body: Array<\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.ClassProperty\n    | t.ClassPrivateProperty\n    | t.ClassAccessorProperty\n    | t.TSDeclareMethod\n    | t.TSIndexSignature\n    | t.StaticBlock\n  >,\n): t.ClassBody {\n  return validateNode<t.ClassBody>({\n    type: \"ClassBody\",\n    body,\n  });\n}\nexport function classExpression(\n  id: t.Identifier | null | undefined = null,\n  superClass: t.Expression | null | undefined = null,\n  body: t.ClassBody,\n  decorators: Array<t.Decorator> | null = null,\n): t.ClassExpression {\n  return validateNode<t.ClassExpression>({\n    type: \"ClassExpression\",\n    id,\n    superClass,\n    body,\n    decorators,\n  });\n}\nexport function classDeclaration(\n  id: t.Identifier,\n  superClass: t.Expression | null | undefined = null,\n  body: t.ClassBody,\n  decorators: Array<t.Decorator> | null = null,\n): t.ClassDeclaration {\n  return validateNode<t.ClassDeclaration>({\n    type: \"ClassDeclaration\",\n    id,\n    superClass,\n    body,\n    decorators,\n  });\n}\nexport function exportAllDeclaration(\n  source: t.StringLiteral,\n): t.ExportAllDeclaration {\n  return validateNode<t.ExportAllDeclaration>({\n    type: \"ExportAllDeclaration\",\n    source,\n  });\n}\nexport function exportDefaultDeclaration(\n  declaration:\n    | t.TSDeclareFunction\n    | t.FunctionDeclaration\n    | t.ClassDeclaration\n    | t.Expression,\n): t.ExportDefaultDeclaration {\n  return validateNode<t.ExportDefaultDeclaration>({\n    type: \"ExportDefaultDeclaration\",\n    declaration,\n  });\n}\nexport function exportNamedDeclaration(\n  declaration: t.Declaration | null = null,\n  specifiers: Array<\n    t.ExportSpecifier | t.ExportDefaultSpecifier | t.ExportNamespaceSpecifier\n  > = [],\n  source: t.StringLiteral | null = null,\n): t.ExportNamedDeclaration {\n  return validateNode<t.ExportNamedDeclaration>({\n    type: \"ExportNamedDeclaration\",\n    declaration,\n    specifiers,\n    source,\n  });\n}\nexport function exportSpecifier(\n  local: t.Identifier,\n  exported: t.Identifier | t.StringLiteral,\n): t.ExportSpecifier {\n  return validateNode<t.ExportSpecifier>({\n    type: \"ExportSpecifier\",\n    local,\n    exported,\n  });\n}\nexport function forOfStatement(\n  left: t.VariableDeclaration | t.LVal,\n  right: t.Expression,\n  body: t.Statement,\n  _await: boolean = false,\n): t.ForOfStatement {\n  return validateNode<t.ForOfStatement>({\n    type: \"ForOfStatement\",\n    left,\n    right,\n    body,\n    await: _await,\n  });\n}\nexport function importDeclaration(\n  specifiers: Array<\n    t.ImportSpecifier | t.ImportDefaultSpecifier | t.ImportNamespaceSpecifier\n  >,\n  source: t.StringLiteral,\n): t.ImportDeclaration {\n  return validateNode<t.ImportDeclaration>({\n    type: \"ImportDeclaration\",\n    specifiers,\n    source,\n  });\n}\nexport function importDefaultSpecifier(\n  local: t.Identifier,\n): t.ImportDefaultSpecifier {\n  return validateNode<t.ImportDefaultSpecifier>({\n    type: \"ImportDefaultSpecifier\",\n    local,\n  });\n}\nexport function importNamespaceSpecifier(\n  local: t.Identifier,\n): t.ImportNamespaceSpecifier {\n  return validateNode<t.ImportNamespaceSpecifier>({\n    type: \"ImportNamespaceSpecifier\",\n    local,\n  });\n}\nexport function importSpecifier(\n  local: t.Identifier,\n  imported: t.Identifier | t.StringLiteral,\n): t.ImportSpecifier {\n  return validateNode<t.ImportSpecifier>({\n    type: \"ImportSpecifier\",\n    local,\n    imported,\n  });\n}\nexport function metaProperty(\n  meta: t.Identifier,\n  property: t.Identifier,\n): t.MetaProperty {\n  return validateNode<t.MetaProperty>({\n    type: \"MetaProperty\",\n    meta,\n    property,\n  });\n}\nexport function classMethod(\n  kind: \"get\" | \"set\" | \"method\" | \"constructor\" | undefined = \"method\",\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  body: t.BlockStatement,\n  computed: boolean = false,\n  _static: boolean = false,\n  generator: boolean = false,\n  async: boolean = false,\n): t.ClassMethod {\n  return validateNode<t.ClassMethod>({\n    type: \"ClassMethod\",\n    kind,\n    key,\n    params,\n    body,\n    computed,\n    static: _static,\n    generator,\n    async,\n  });\n}\nexport function objectPattern(\n  properties: Array<t.RestElement | t.ObjectProperty>,\n): t.ObjectPattern {\n  return validateNode<t.ObjectPattern>({\n    type: \"ObjectPattern\",\n    properties,\n  });\n}\nexport function spreadElement(argument: t.Expression): t.SpreadElement {\n  return validateNode<t.SpreadElement>({\n    type: \"SpreadElement\",\n    argument,\n  });\n}\nfunction _super(): t.Super {\n  return {\n    type: \"Super\",\n  };\n}\nexport { _super as super };\nexport function taggedTemplateExpression(\n  tag: t.Expression,\n  quasi: t.TemplateLiteral,\n): t.TaggedTemplateExpression {\n  return validateNode<t.TaggedTemplateExpression>({\n    type: \"TaggedTemplateExpression\",\n    tag,\n    quasi,\n  });\n}\nexport function templateElement(\n  value: { raw: string; cooked?: string },\n  tail: boolean = false,\n): t.TemplateElement {\n  return validateNode<t.TemplateElement>({\n    type: \"TemplateElement\",\n    value,\n    tail,\n  });\n}\nexport function templateLiteral(\n  quasis: Array<t.TemplateElement>,\n  expressions: Array<t.Expression | t.TSType>,\n): t.TemplateLiteral {\n  return validateNode<t.TemplateLiteral>({\n    type: \"TemplateLiteral\",\n    quasis,\n    expressions,\n  });\n}\nexport function yieldExpression(\n  argument: t.Expression | null = null,\n  delegate: boolean = false,\n): t.YieldExpression {\n  return validateNode<t.YieldExpression>({\n    type: \"YieldExpression\",\n    argument,\n    delegate,\n  });\n}\nexport function awaitExpression(argument: t.Expression): t.AwaitExpression {\n  return validateNode<t.AwaitExpression>({\n    type: \"AwaitExpression\",\n    argument,\n  });\n}\nfunction _import(): t.Import {\n  return {\n    type: \"Import\",\n  };\n}\nexport { _import as import };\nexport function bigIntLiteral(value: string): t.BigIntLiteral {\n  return validateNode<t.BigIntLiteral>({\n    type: \"BigIntLiteral\",\n    value,\n  });\n}\nexport function exportNamespaceSpecifier(\n  exported: t.Identifier,\n): t.ExportNamespaceSpecifier {\n  return validateNode<t.ExportNamespaceSpecifier>({\n    type: \"ExportNamespaceSpecifier\",\n    exported,\n  });\n}\nexport function optionalMemberExpression(\n  object: t.Expression,\n  property: t.Expression | t.Identifier,\n  computed: boolean | undefined = false,\n  optional: boolean,\n): t.OptionalMemberExpression {\n  return validateNode<t.OptionalMemberExpression>({\n    type: \"OptionalMemberExpression\",\n    object,\n    property,\n    computed,\n    optional,\n  });\n}\nexport function optionalCallExpression(\n  callee: t.Expression,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n  optional: boolean,\n): t.OptionalCallExpression {\n  return validateNode<t.OptionalCallExpression>({\n    type: \"OptionalCallExpression\",\n    callee,\n    arguments: _arguments,\n    optional,\n  });\n}\nexport function classProperty(\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  value: t.Expression | null = null,\n  typeAnnotation: t.TypeAnnotation | t.TSTypeAnnotation | t.Noop | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  computed: boolean = false,\n  _static: boolean = false,\n): t.ClassProperty {\n  return validateNode<t.ClassProperty>({\n    type: \"ClassProperty\",\n    key,\n    value,\n    typeAnnotation,\n    decorators,\n    computed,\n    static: _static,\n  });\n}\nexport function classAccessorProperty(\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression\n    | t.PrivateName,\n  value: t.Expression | null = null,\n  typeAnnotation: t.TypeAnnotation | t.TSTypeAnnotation | t.Noop | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  computed: boolean = false,\n  _static: boolean = false,\n): t.ClassAccessorProperty {\n  return validateNode<t.ClassAccessorProperty>({\n    type: \"ClassAccessorProperty\",\n    key,\n    value,\n    typeAnnotation,\n    decorators,\n    computed,\n    static: _static,\n  });\n}\nexport function classPrivateProperty(\n  key: t.PrivateName,\n  value: t.Expression | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  _static: boolean = false,\n): t.ClassPrivateProperty {\n  return validateNode<t.ClassPrivateProperty>({\n    type: \"ClassPrivateProperty\",\n    key,\n    value,\n    decorators,\n    static: _static,\n  });\n}\nexport function classPrivateMethod(\n  kind: \"get\" | \"set\" | \"method\" | undefined = \"method\",\n  key: t.PrivateName,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  body: t.BlockStatement,\n  _static: boolean = false,\n): t.ClassPrivateMethod {\n  return validateNode<t.ClassPrivateMethod>({\n    type: \"ClassPrivateMethod\",\n    kind,\n    key,\n    params,\n    body,\n    static: _static,\n  });\n}\nexport function privateName(id: t.Identifier): t.PrivateName {\n  return validateNode<t.PrivateName>({\n    type: \"PrivateName\",\n    id,\n  });\n}\nexport function staticBlock(body: Array<t.Statement>): t.StaticBlock {\n  return validateNode<t.StaticBlock>({\n    type: \"StaticBlock\",\n    body,\n  });\n}\nexport function anyTypeAnnotation(): t.AnyTypeAnnotation {\n  return {\n    type: \"AnyTypeAnnotation\",\n  };\n}\nexport function arrayTypeAnnotation(\n  elementType: t.FlowType,\n): t.ArrayTypeAnnotation {\n  return validateNode<t.ArrayTypeAnnotation>({\n    type: \"ArrayTypeAnnotation\",\n    elementType,\n  });\n}\nexport function booleanTypeAnnotation(): t.BooleanTypeAnnotation {\n  return {\n    type: \"BooleanTypeAnnotation\",\n  };\n}\nexport function booleanLiteralTypeAnnotation(\n  value: boolean,\n): t.BooleanLiteralTypeAnnotation {\n  return validateNode<t.BooleanLiteralTypeAnnotation>({\n    type: \"BooleanLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function nullLiteralTypeAnnotation(): t.NullLiteralTypeAnnotation {\n  return {\n    type: \"NullLiteralTypeAnnotation\",\n  };\n}\nexport function classImplements(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.ClassImplements {\n  return validateNode<t.ClassImplements>({\n    type: \"ClassImplements\",\n    id,\n    typeParameters,\n  });\n}\nexport function declareClass(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.DeclareClass {\n  return validateNode<t.DeclareClass>({\n    type: \"DeclareClass\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function declareFunction(id: t.Identifier): t.DeclareFunction {\n  return validateNode<t.DeclareFunction>({\n    type: \"DeclareFunction\",\n    id,\n  });\n}\nexport function declareInterface(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.DeclareInterface {\n  return validateNode<t.DeclareInterface>({\n    type: \"DeclareInterface\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function declareModule(\n  id: t.Identifier | t.StringLiteral,\n  body: t.BlockStatement,\n  kind: \"CommonJS\" | \"ES\" | null = null,\n): t.DeclareModule {\n  return validateNode<t.DeclareModule>({\n    type: \"DeclareModule\",\n    id,\n    body,\n    kind,\n  });\n}\nexport function declareModuleExports(\n  typeAnnotation: t.TypeAnnotation,\n): t.DeclareModuleExports {\n  return validateNode<t.DeclareModuleExports>({\n    type: \"DeclareModuleExports\",\n    typeAnnotation,\n  });\n}\nexport function declareTypeAlias(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  right: t.FlowType,\n): t.DeclareTypeAlias {\n  return validateNode<t.DeclareTypeAlias>({\n    type: \"DeclareTypeAlias\",\n    id,\n    typeParameters,\n    right,\n  });\n}\nexport function declareOpaqueType(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null = null,\n  supertype: t.FlowType | null = null,\n): t.DeclareOpaqueType {\n  return validateNode<t.DeclareOpaqueType>({\n    type: \"DeclareOpaqueType\",\n    id,\n    typeParameters,\n    supertype,\n  });\n}\nexport function declareVariable(id: t.Identifier): t.DeclareVariable {\n  return validateNode<t.DeclareVariable>({\n    type: \"DeclareVariable\",\n    id,\n  });\n}\nexport function declareExportDeclaration(\n  declaration: t.Flow | null = null,\n  specifiers: Array<\n    t.ExportSpecifier | t.ExportNamespaceSpecifier\n  > | null = null,\n  source: t.StringLiteral | null = null,\n): t.DeclareExportDeclaration {\n  return validateNode<t.DeclareExportDeclaration>({\n    type: \"DeclareExportDeclaration\",\n    declaration,\n    specifiers,\n    source,\n  });\n}\nexport function declareExportAllDeclaration(\n  source: t.StringLiteral,\n): t.DeclareExportAllDeclaration {\n  return validateNode<t.DeclareExportAllDeclaration>({\n    type: \"DeclareExportAllDeclaration\",\n    source,\n  });\n}\nexport function declaredPredicate(value: t.Flow): t.DeclaredPredicate {\n  return validateNode<t.DeclaredPredicate>({\n    type: \"DeclaredPredicate\",\n    value,\n  });\n}\nexport function existsTypeAnnotation(): t.ExistsTypeAnnotation {\n  return {\n    type: \"ExistsTypeAnnotation\",\n  };\n}\nexport function functionTypeAnnotation(\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  params: Array<t.FunctionTypeParam>,\n  rest: t.FunctionTypeParam | null | undefined = null,\n  returnType: t.FlowType,\n): t.FunctionTypeAnnotation {\n  return validateNode<t.FunctionTypeAnnotation>({\n    type: \"FunctionTypeAnnotation\",\n    typeParameters,\n    params,\n    rest,\n    returnType,\n  });\n}\nexport function functionTypeParam(\n  name: t.Identifier | null | undefined = null,\n  typeAnnotation: t.FlowType,\n): t.FunctionTypeParam {\n  return validateNode<t.FunctionTypeParam>({\n    type: \"FunctionTypeParam\",\n    name,\n    typeAnnotation,\n  });\n}\nexport function genericTypeAnnotation(\n  id: t.Identifier | t.QualifiedTypeIdentifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.GenericTypeAnnotation {\n  return validateNode<t.GenericTypeAnnotation>({\n    type: \"GenericTypeAnnotation\",\n    id,\n    typeParameters,\n  });\n}\nexport function inferredPredicate(): t.InferredPredicate {\n  return {\n    type: \"InferredPredicate\",\n  };\n}\nexport function interfaceExtends(\n  id: t.Identifier | t.QualifiedTypeIdentifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.InterfaceExtends {\n  return validateNode<t.InterfaceExtends>({\n    type: \"InterfaceExtends\",\n    id,\n    typeParameters,\n  });\n}\nexport function interfaceDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.InterfaceDeclaration {\n  return validateNode<t.InterfaceDeclaration>({\n    type: \"InterfaceDeclaration\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function interfaceTypeAnnotation(\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.InterfaceTypeAnnotation {\n  return validateNode<t.InterfaceTypeAnnotation>({\n    type: \"InterfaceTypeAnnotation\",\n    extends: _extends,\n    body,\n  });\n}\nexport function intersectionTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.IntersectionTypeAnnotation {\n  return validateNode<t.IntersectionTypeAnnotation>({\n    type: \"IntersectionTypeAnnotation\",\n    types,\n  });\n}\nexport function mixedTypeAnnotation(): t.MixedTypeAnnotation {\n  return {\n    type: \"MixedTypeAnnotation\",\n  };\n}\nexport function emptyTypeAnnotation(): t.EmptyTypeAnnotation {\n  return {\n    type: \"EmptyTypeAnnotation\",\n  };\n}\nexport function nullableTypeAnnotation(\n  typeAnnotation: t.FlowType,\n): t.NullableTypeAnnotation {\n  return validateNode<t.NullableTypeAnnotation>({\n    type: \"NullableTypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport function numberLiteralTypeAnnotation(\n  value: number,\n): t.NumberLiteralTypeAnnotation {\n  return validateNode<t.NumberLiteralTypeAnnotation>({\n    type: \"NumberLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function numberTypeAnnotation(): t.NumberTypeAnnotation {\n  return {\n    type: \"NumberTypeAnnotation\",\n  };\n}\nexport function objectTypeAnnotation(\n  properties: Array<t.ObjectTypeProperty | t.ObjectTypeSpreadProperty>,\n  indexers: Array<t.ObjectTypeIndexer> = [],\n  callProperties: Array<t.ObjectTypeCallProperty> = [],\n  internalSlots: Array<t.ObjectTypeInternalSlot> = [],\n  exact: boolean = false,\n): t.ObjectTypeAnnotation {\n  return validateNode<t.ObjectTypeAnnotation>({\n    type: \"ObjectTypeAnnotation\",\n    properties,\n    indexers,\n    callProperties,\n    internalSlots,\n    exact,\n  });\n}\nexport function objectTypeInternalSlot(\n  id: t.Identifier,\n  value: t.FlowType,\n  optional: boolean,\n  _static: boolean,\n  method: boolean,\n): t.ObjectTypeInternalSlot {\n  return validateNode<t.ObjectTypeInternalSlot>({\n    type: \"ObjectTypeInternalSlot\",\n    id,\n    value,\n    optional,\n    static: _static,\n    method,\n  });\n}\nexport function objectTypeCallProperty(\n  value: t.FlowType,\n): t.ObjectTypeCallProperty {\n  return validateNode<t.ObjectTypeCallProperty>({\n    type: \"ObjectTypeCallProperty\",\n    value,\n    static: null,\n  });\n}\nexport function objectTypeIndexer(\n  id: t.Identifier | null | undefined = null,\n  key: t.FlowType,\n  value: t.FlowType,\n  variance: t.Variance | null = null,\n): t.ObjectTypeIndexer {\n  return validateNode<t.ObjectTypeIndexer>({\n    type: \"ObjectTypeIndexer\",\n    id,\n    key,\n    value,\n    variance,\n    static: null,\n  });\n}\nexport function objectTypeProperty(\n  key: t.Identifier | t.StringLiteral,\n  value: t.FlowType,\n  variance: t.Variance | null = null,\n): t.ObjectTypeProperty {\n  return validateNode<t.ObjectTypeProperty>({\n    type: \"ObjectTypeProperty\",\n    key,\n    value,\n    variance,\n    kind: null,\n    method: null,\n    optional: null,\n    proto: null,\n    static: null,\n  });\n}\nexport function objectTypeSpreadProperty(\n  argument: t.FlowType,\n): t.ObjectTypeSpreadProperty {\n  return validateNode<t.ObjectTypeSpreadProperty>({\n    type: \"ObjectTypeSpreadProperty\",\n    argument,\n  });\n}\nexport function opaqueType(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  supertype: t.FlowType | null | undefined = null,\n  impltype: t.FlowType,\n): t.OpaqueType {\n  return validateNode<t.OpaqueType>({\n    type: \"OpaqueType\",\n    id,\n    typeParameters,\n    supertype,\n    impltype,\n  });\n}\nexport function qualifiedTypeIdentifier(\n  id: t.Identifier,\n  qualification: t.Identifier | t.QualifiedTypeIdentifier,\n): t.QualifiedTypeIdentifier {\n  return validateNode<t.QualifiedTypeIdentifier>({\n    type: \"QualifiedTypeIdentifier\",\n    id,\n    qualification,\n  });\n}\nexport function stringLiteralTypeAnnotation(\n  value: string,\n): t.StringLiteralTypeAnnotation {\n  return validateNode<t.StringLiteralTypeAnnotation>({\n    type: \"StringLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function stringTypeAnnotation(): t.StringTypeAnnotation {\n  return {\n    type: \"StringTypeAnnotation\",\n  };\n}\nexport function symbolTypeAnnotation(): t.SymbolTypeAnnotation {\n  return {\n    type: \"SymbolTypeAnnotation\",\n  };\n}\nexport function thisTypeAnnotation(): t.ThisTypeAnnotation {\n  return {\n    type: \"ThisTypeAnnotation\",\n  };\n}\nexport function tupleTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.TupleTypeAnnotation {\n  return validateNode<t.TupleTypeAnnotation>({\n    type: \"TupleTypeAnnotation\",\n    types,\n  });\n}\nexport function typeofTypeAnnotation(\n  argument: t.FlowType,\n): t.TypeofTypeAnnotation {\n  return validateNode<t.TypeofTypeAnnotation>({\n    type: \"TypeofTypeAnnotation\",\n    argument,\n  });\n}\nexport function typeAlias(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  right: t.FlowType,\n): t.TypeAlias {\n  return validateNode<t.TypeAlias>({\n    type: \"TypeAlias\",\n    id,\n    typeParameters,\n    right,\n  });\n}\nexport function typeAnnotation(typeAnnotation: t.FlowType): t.TypeAnnotation {\n  return validateNode<t.TypeAnnotation>({\n    type: \"TypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport function typeCastExpression(\n  expression: t.Expression,\n  typeAnnotation: t.TypeAnnotation,\n): t.TypeCastExpression {\n  return validateNode<t.TypeCastExpression>({\n    type: \"TypeCastExpression\",\n    expression,\n    typeAnnotation,\n  });\n}\nexport function typeParameter(\n  bound: t.TypeAnnotation | null = null,\n  _default: t.FlowType | null = null,\n  variance: t.Variance | null = null,\n): t.TypeParameter {\n  return validateNode<t.TypeParameter>({\n    type: \"TypeParameter\",\n    bound,\n    default: _default,\n    variance,\n    name: null,\n  });\n}\nexport function typeParameterDeclaration(\n  params: Array<t.TypeParameter>,\n): t.TypeParameterDeclaration {\n  return validateNode<t.TypeParameterDeclaration>({\n    type: \"TypeParameterDeclaration\",\n    params,\n  });\n}\nexport function typeParameterInstantiation(\n  params: Array<t.FlowType>,\n): t.TypeParameterInstantiation {\n  return validateNode<t.TypeParameterInstantiation>({\n    type: \"TypeParameterInstantiation\",\n    params,\n  });\n}\nexport function unionTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.UnionTypeAnnotation {\n  return validateNode<t.UnionTypeAnnotation>({\n    type: \"UnionTypeAnnotation\",\n    types,\n  });\n}\nexport function variance(kind: \"minus\" | \"plus\"): t.Variance {\n  return validateNode<t.Variance>({\n    type: \"Variance\",\n    kind,\n  });\n}\nexport function voidTypeAnnotation(): t.VoidTypeAnnotation {\n  return {\n    type: \"VoidTypeAnnotation\",\n  };\n}\nexport function enumDeclaration(\n  id: t.Identifier,\n  body:\n    | t.EnumBooleanBody\n    | t.EnumNumberBody\n    | t.EnumStringBody\n    | t.EnumSymbolBody,\n): t.EnumDeclaration {\n  return validateNode<t.EnumDeclaration>({\n    type: \"EnumDeclaration\",\n    id,\n    body,\n  });\n}\nexport function enumBooleanBody(\n  members: Array<t.EnumBooleanMember>,\n): t.EnumBooleanBody {\n  return validateNode<t.EnumBooleanBody>({\n    type: \"EnumBooleanBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumNumberBody(\n  members: Array<t.EnumNumberMember>,\n): t.EnumNumberBody {\n  return validateNode<t.EnumNumberBody>({\n    type: \"EnumNumberBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumStringBody(\n  members: Array<t.EnumStringMember | t.EnumDefaultedMember>,\n): t.EnumStringBody {\n  return validateNode<t.EnumStringBody>({\n    type: \"EnumStringBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumSymbolBody(\n  members: Array<t.EnumDefaultedMember>,\n): t.EnumSymbolBody {\n  return validateNode<t.EnumSymbolBody>({\n    type: \"EnumSymbolBody\",\n    members,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumBooleanMember(id: t.Identifier): t.EnumBooleanMember {\n  return validateNode<t.EnumBooleanMember>({\n    type: \"EnumBooleanMember\",\n    id,\n    init: null,\n  });\n}\nexport function enumNumberMember(\n  id: t.Identifier,\n  init: t.NumericLiteral,\n): t.EnumNumberMember {\n  return validateNode<t.EnumNumberMember>({\n    type: \"EnumNumberMember\",\n    id,\n    init,\n  });\n}\nexport function enumStringMember(\n  id: t.Identifier,\n  init: t.StringLiteral,\n): t.EnumStringMember {\n  return validateNode<t.EnumStringMember>({\n    type: \"EnumStringMember\",\n    id,\n    init,\n  });\n}\nexport function enumDefaultedMember(id: t.Identifier): t.EnumDefaultedMember {\n  return validateNode<t.EnumDefaultedMember>({\n    type: \"EnumDefaultedMember\",\n    id,\n  });\n}\nexport function indexedAccessType(\n  objectType: t.FlowType,\n  indexType: t.FlowType,\n): t.IndexedAccessType {\n  return validateNode<t.IndexedAccessType>({\n    type: \"IndexedAccessType\",\n    objectType,\n    indexType,\n  });\n}\nexport function optionalIndexedAccessType(\n  objectType: t.FlowType,\n  indexType: t.FlowType,\n): t.OptionalIndexedAccessType {\n  return validateNode<t.OptionalIndexedAccessType>({\n    type: \"OptionalIndexedAccessType\",\n    objectType,\n    indexType,\n    optional: null,\n  });\n}\nexport function jsxAttribute(\n  name: t.JSXIdentifier | t.JSXNamespacedName,\n  value:\n    | t.JSXElement\n    | t.JSXFragment\n    | t.StringLiteral\n    | t.JSXExpressionContainer\n    | null = null,\n): t.JSXAttribute {\n  return validateNode<t.JSXAttribute>({\n    type: \"JSXAttribute\",\n    name,\n    value,\n  });\n}\nexport { jsxAttribute as jSXAttribute };\nexport function jsxClosingElement(\n  name: t.JSXIdentifier | t.JSXMemberExpression | t.JSXNamespacedName,\n): t.JSXClosingElement {\n  return validateNode<t.JSXClosingElement>({\n    type: \"JSXClosingElement\",\n    name,\n  });\n}\nexport { jsxClosingElement as jSXClosingElement };\nexport function jsxElement(\n  openingElement: t.JSXOpeningElement,\n  closingElement: t.JSXClosingElement | null | undefined = null,\n  children: Array<\n    | t.JSXText\n    | t.JSXExpressionContainer\n    | t.JSXSpreadChild\n    | t.JSXElement\n    | t.JSXFragment\n  >,\n  selfClosing: boolean | null = null,\n): t.JSXElement {\n  return validateNode<t.JSXElement>({\n    type: \"JSXElement\",\n    openingElement,\n    closingElement,\n    children,\n    selfClosing,\n  });\n}\nexport { jsxElement as jSXElement };\nexport function jsxEmptyExpression(): t.JSXEmptyExpression {\n  return {\n    type: \"JSXEmptyExpression\",\n  };\n}\nexport { jsxEmptyExpression as jSXEmptyExpression };\nexport function jsxExpressionContainer(\n  expression: t.Expression | t.JSXEmptyExpression,\n): t.JSXExpressionContainer {\n  return validateNode<t.JSXExpressionContainer>({\n    type: \"JSXExpressionContainer\",\n    expression,\n  });\n}\nexport { jsxExpressionContainer as jSXExpressionContainer };\nexport function jsxSpreadChild(expression: t.Expression): t.JSXSpreadChild {\n  return validateNode<t.JSXSpreadChild>({\n    type: \"JSXSpreadChild\",\n    expression,\n  });\n}\nexport { jsxSpreadChild as jSXSpreadChild };\nexport function jsxIdentifier(name: string): t.JSXIdentifier {\n  return validateNode<t.JSXIdentifier>({\n    type: \"JSXIdentifier\",\n    name,\n  });\n}\nexport { jsxIdentifier as jSXIdentifier };\nexport function jsxMemberExpression(\n  object: t.JSXMemberExpression | t.JSXIdentifier,\n  property: t.JSXIdentifier,\n): t.JSXMemberExpression {\n  return validateNode<t.JSXMemberExpression>({\n    type: \"JSXMemberExpression\",\n    object,\n    property,\n  });\n}\nexport { jsxMemberExpression as jSXMemberExpression };\nexport function jsxNamespacedName(\n  namespace: t.JSXIdentifier,\n  name: t.JSXIdentifier,\n): t.JSXNamespacedName {\n  return validateNode<t.JSXNamespacedName>({\n    type: \"JSXNamespacedName\",\n    namespace,\n    name,\n  });\n}\nexport { jsxNamespacedName as jSXNamespacedName };\nexport function jsxOpeningElement(\n  name: t.JSXIdentifier | t.JSXMemberExpression | t.JSXNamespacedName,\n  attributes: Array<t.JSXAttribute | t.JSXSpreadAttribute>,\n  selfClosing: boolean = false,\n): t.JSXOpeningElement {\n  return validateNode<t.JSXOpeningElement>({\n    type: \"JSXOpeningElement\",\n    name,\n    attributes,\n    selfClosing,\n  });\n}\nexport { jsxOpeningElement as jSXOpeningElement };\nexport function jsxSpreadAttribute(\n  argument: t.Expression,\n): t.JSXSpreadAttribute {\n  return validateNode<t.JSXSpreadAttribute>({\n    type: \"JSXSpreadAttribute\",\n    argument,\n  });\n}\nexport { jsxSpreadAttribute as jSXSpreadAttribute };\nexport function jsxText(value: string): t.JSXText {\n  return validateNode<t.JSXText>({\n    type: \"JSXText\",\n    value,\n  });\n}\nexport { jsxText as jSXText };\nexport function jsxFragment(\n  openingFragment: t.JSXOpeningFragment,\n  closingFragment: t.JSXClosingFragment,\n  children: Array<\n    | t.JSXText\n    | t.JSXExpressionContainer\n    | t.JSXSpreadChild\n    | t.JSXElement\n    | t.JSXFragment\n  >,\n): t.JSXFragment {\n  return validateNode<t.JSXFragment>({\n    type: \"JSXFragment\",\n    openingFragment,\n    closingFragment,\n    children,\n  });\n}\nexport { jsxFragment as jSXFragment };\nexport function jsxOpeningFragment(): t.JSXOpeningFragment {\n  return {\n    type: \"JSXOpeningFragment\",\n  };\n}\nexport { jsxOpeningFragment as jSXOpeningFragment };\nexport function jsxClosingFragment(): t.JSXClosingFragment {\n  return {\n    type: \"JSXClosingFragment\",\n  };\n}\nexport { jsxClosingFragment as jSXClosingFragment };\nexport function noop(): t.Noop {\n  return {\n    type: \"Noop\",\n  };\n}\nexport function placeholder(\n  expectedNode:\n    | \"Identifier\"\n    | \"StringLiteral\"\n    | \"Expression\"\n    | \"Statement\"\n    | \"Declaration\"\n    | \"BlockStatement\"\n    | \"ClassBody\"\n    | \"Pattern\",\n  name: t.Identifier,\n): t.Placeholder {\n  return validateNode<t.Placeholder>({\n    type: \"Placeholder\",\n    expectedNode,\n    name,\n  });\n}\nexport function v8IntrinsicIdentifier(name: string): t.V8IntrinsicIdentifier {\n  return validateNode<t.V8IntrinsicIdentifier>({\n    type: \"V8IntrinsicIdentifier\",\n    name,\n  });\n}\nexport function argumentPlaceholder(): t.ArgumentPlaceholder {\n  return {\n    type: \"ArgumentPlaceholder\",\n  };\n}\nexport function bindExpression(\n  object: t.Expression,\n  callee: t.Expression,\n): t.BindExpression {\n  return validateNode<t.BindExpression>({\n    type: \"BindExpression\",\n    object,\n    callee,\n  });\n}\nexport function importAttribute(\n  key: t.Identifier | t.StringLiteral,\n  value: t.StringLiteral,\n): t.ImportAttribute {\n  return validateNode<t.ImportAttribute>({\n    type: \"ImportAttribute\",\n    key,\n    value,\n  });\n}\nexport function decorator(expression: t.Expression): t.Decorator {\n  return validateNode<t.Decorator>({\n    type: \"Decorator\",\n    expression,\n  });\n}\nexport function doExpression(\n  body: t.BlockStatement,\n  async: boolean = false,\n): t.DoExpression {\n  return validateNode<t.DoExpression>({\n    type: \"DoExpression\",\n    body,\n    async,\n  });\n}\nexport function exportDefaultSpecifier(\n  exported: t.Identifier,\n): t.ExportDefaultSpecifier {\n  return validateNode<t.ExportDefaultSpecifier>({\n    type: \"ExportDefaultSpecifier\",\n    exported,\n  });\n}\nexport function recordExpression(\n  properties: Array<t.ObjectProperty | t.SpreadElement>,\n): t.RecordExpression {\n  return validateNode<t.RecordExpression>({\n    type: \"RecordExpression\",\n    properties,\n  });\n}\nexport function tupleExpression(\n  elements: Array<t.Expression | t.SpreadElement> = [],\n): t.TupleExpression {\n  return validateNode<t.TupleExpression>({\n    type: \"TupleExpression\",\n    elements,\n  });\n}\nexport function decimalLiteral(value: string): t.DecimalLiteral {\n  return validateNode<t.DecimalLiteral>({\n    type: \"DecimalLiteral\",\n    value,\n  });\n}\nexport function moduleExpression(body: t.Program): t.ModuleExpression {\n  return validateNode<t.ModuleExpression>({\n    type: \"ModuleExpression\",\n    body,\n  });\n}\nexport function topicReference(): t.TopicReference {\n  return {\n    type: \"TopicReference\",\n  };\n}\nexport function pipelineTopicExpression(\n  expression: t.Expression,\n): t.PipelineTopicExpression {\n  return validateNode<t.PipelineTopicExpression>({\n    type: \"PipelineTopicExpression\",\n    expression,\n  });\n}\nexport function pipelineBareFunction(\n  callee: t.Expression,\n): t.PipelineBareFunction {\n  return validateNode<t.PipelineBareFunction>({\n    type: \"PipelineBareFunction\",\n    callee,\n  });\n}\nexport function pipelinePrimaryTopicReference(): t.PipelinePrimaryTopicReference {\n  return {\n    type: \"PipelinePrimaryTopicReference\",\n  };\n}\nexport function tsParameterProperty(\n  parameter: t.Identifier | t.AssignmentPattern,\n): t.TSParameterProperty {\n  return validateNode<t.TSParameterProperty>({\n    type: \"TSParameterProperty\",\n    parameter,\n  });\n}\nexport { tsParameterProperty as tSParameterProperty };\nexport function tsDeclareFunction(\n  id: t.Identifier | null | undefined = null,\n  typeParameters:\n    | t.TSTypeParameterDeclaration\n    | t.Noop\n    | null\n    | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  returnType: t.TSTypeAnnotation | t.Noop | null = null,\n): t.TSDeclareFunction {\n  return validateNode<t.TSDeclareFunction>({\n    type: \"TSDeclareFunction\",\n    id,\n    typeParameters,\n    params,\n    returnType,\n  });\n}\nexport { tsDeclareFunction as tSDeclareFunction };\nexport function tsDeclareMethod(\n  decorators: Array<t.Decorator> | null | undefined = null,\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  typeParameters:\n    | t.TSTypeParameterDeclaration\n    | t.Noop\n    | null\n    | undefined = null,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  returnType: t.TSTypeAnnotation | t.Noop | null = null,\n): t.TSDeclareMethod {\n  return validateNode<t.TSDeclareMethod>({\n    type: \"TSDeclareMethod\",\n    decorators,\n    key,\n    typeParameters,\n    params,\n    returnType,\n  });\n}\nexport { tsDeclareMethod as tSDeclareMethod };\nexport function tsQualifiedName(\n  left: t.TSEntityName,\n  right: t.Identifier,\n): t.TSQualifiedName {\n  return validateNode<t.TSQualifiedName>({\n    type: \"TSQualifiedName\",\n    left,\n    right,\n  });\n}\nexport { tsQualifiedName as tSQualifiedName };\nexport function tsCallSignatureDeclaration(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSCallSignatureDeclaration {\n  return validateNode<t.TSCallSignatureDeclaration>({\n    type: \"TSCallSignatureDeclaration\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsCallSignatureDeclaration as tSCallSignatureDeclaration };\nexport function tsConstructSignatureDeclaration(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSConstructSignatureDeclaration {\n  return validateNode<t.TSConstructSignatureDeclaration>({\n    type: \"TSConstructSignatureDeclaration\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsConstructSignatureDeclaration as tSConstructSignatureDeclaration };\nexport function tsPropertySignature(\n  key: t.Expression,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n  initializer: t.Expression | null = null,\n): t.TSPropertySignature {\n  return validateNode<t.TSPropertySignature>({\n    type: \"TSPropertySignature\",\n    key,\n    typeAnnotation,\n    initializer,\n    kind: null,\n  });\n}\nexport { tsPropertySignature as tSPropertySignature };\nexport function tsMethodSignature(\n  key: t.Expression,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSMethodSignature {\n  return validateNode<t.TSMethodSignature>({\n    type: \"TSMethodSignature\",\n    key,\n    typeParameters,\n    parameters,\n    typeAnnotation,\n    kind: null,\n  });\n}\nexport { tsMethodSignature as tSMethodSignature };\nexport function tsIndexSignature(\n  parameters: Array<t.Identifier>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSIndexSignature {\n  return validateNode<t.TSIndexSignature>({\n    type: \"TSIndexSignature\",\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsIndexSignature as tSIndexSignature };\nexport function tsAnyKeyword(): t.TSAnyKeyword {\n  return {\n    type: \"TSAnyKeyword\",\n  };\n}\nexport { tsAnyKeyword as tSAnyKeyword };\nexport function tsBooleanKeyword(): t.TSBooleanKeyword {\n  return {\n    type: \"TSBooleanKeyword\",\n  };\n}\nexport { tsBooleanKeyword as tSBooleanKeyword };\nexport function tsBigIntKeyword(): t.TSBigIntKeyword {\n  return {\n    type: \"TSBigIntKeyword\",\n  };\n}\nexport { tsBigIntKeyword as tSBigIntKeyword };\nexport function tsIntrinsicKeyword(): t.TSIntrinsicKeyword {\n  return {\n    type: \"TSIntrinsicKeyword\",\n  };\n}\nexport { tsIntrinsicKeyword as tSIntrinsicKeyword };\nexport function tsNeverKeyword(): t.TSNeverKeyword {\n  return {\n    type: \"TSNeverKeyword\",\n  };\n}\nexport { tsNeverKeyword as tSNeverKeyword };\nexport function tsNullKeyword(): t.TSNullKeyword {\n  return {\n    type: \"TSNullKeyword\",\n  };\n}\nexport { tsNullKeyword as tSNullKeyword };\nexport function tsNumberKeyword(): t.TSNumberKeyword {\n  return {\n    type: \"TSNumberKeyword\",\n  };\n}\nexport { tsNumberKeyword as tSNumberKeyword };\nexport function tsObjectKeyword(): t.TSObjectKeyword {\n  return {\n    type: \"TSObjectKeyword\",\n  };\n}\nexport { tsObjectKeyword as tSObjectKeyword };\nexport function tsStringKeyword(): t.TSStringKeyword {\n  return {\n    type: \"TSStringKeyword\",\n  };\n}\nexport { tsStringKeyword as tSStringKeyword };\nexport function tsSymbolKeyword(): t.TSSymbolKeyword {\n  return {\n    type: \"TSSymbolKeyword\",\n  };\n}\nexport { tsSymbolKeyword as tSSymbolKeyword };\nexport function tsUndefinedKeyword(): t.TSUndefinedKeyword {\n  return {\n    type: \"TSUndefinedKeyword\",\n  };\n}\nexport { tsUndefinedKeyword as tSUndefinedKeyword };\nexport function tsUnknownKeyword(): t.TSUnknownKeyword {\n  return {\n    type: \"TSUnknownKeyword\",\n  };\n}\nexport { tsUnknownKeyword as tSUnknownKeyword };\nexport function tsVoidKeyword(): t.TSVoidKeyword {\n  return {\n    type: \"TSVoidKeyword\",\n  };\n}\nexport { tsVoidKeyword as tSVoidKeyword };\nexport function tsThisType(): t.TSThisType {\n  return {\n    type: \"TSThisType\",\n  };\n}\nexport { tsThisType as tSThisType };\nexport function tsFunctionType(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSFunctionType {\n  return validateNode<t.TSFunctionType>({\n    type: \"TSFunctionType\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsFunctionType as tSFunctionType };\nexport function tsConstructorType(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSConstructorType {\n  return validateNode<t.TSConstructorType>({\n    type: \"TSConstructorType\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsConstructorType as tSConstructorType };\nexport function tsTypeReference(\n  typeName: t.TSEntityName,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSTypeReference {\n  return validateNode<t.TSTypeReference>({\n    type: \"TSTypeReference\",\n    typeName,\n    typeParameters,\n  });\n}\nexport { tsTypeReference as tSTypeReference };\nexport function tsTypePredicate(\n  parameterName: t.Identifier | t.TSThisType,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n  asserts: boolean | null = null,\n): t.TSTypePredicate {\n  return validateNode<t.TSTypePredicate>({\n    type: \"TSTypePredicate\",\n    parameterName,\n    typeAnnotation,\n    asserts,\n  });\n}\nexport { tsTypePredicate as tSTypePredicate };\nexport function tsTypeQuery(\n  exprName: t.TSEntityName | t.TSImportType,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSTypeQuery {\n  return validateNode<t.TSTypeQuery>({\n    type: \"TSTypeQuery\",\n    exprName,\n    typeParameters,\n  });\n}\nexport { tsTypeQuery as tSTypeQuery };\nexport function tsTypeLiteral(\n  members: Array<t.TSTypeElement>,\n): t.TSTypeLiteral {\n  return validateNode<t.TSTypeLiteral>({\n    type: \"TSTypeLiteral\",\n    members,\n  });\n}\nexport { tsTypeLiteral as tSTypeLiteral };\nexport function tsArrayType(elementType: t.TSType): t.TSArrayType {\n  return validateNode<t.TSArrayType>({\n    type: \"TSArrayType\",\n    elementType,\n  });\n}\nexport { tsArrayType as tSArrayType };\nexport function tsTupleType(\n  elementTypes: Array<t.TSType | t.TSNamedTupleMember>,\n): t.TSTupleType {\n  return validateNode<t.TSTupleType>({\n    type: \"TSTupleType\",\n    elementTypes,\n  });\n}\nexport { tsTupleType as tSTupleType };\nexport function tsOptionalType(typeAnnotation: t.TSType): t.TSOptionalType {\n  return validateNode<t.TSOptionalType>({\n    type: \"TSOptionalType\",\n    typeAnnotation,\n  });\n}\nexport { tsOptionalType as tSOptionalType };\nexport function tsRestType(typeAnnotation: t.TSType): t.TSRestType {\n  return validateNode<t.TSRestType>({\n    type: \"TSRestType\",\n    typeAnnotation,\n  });\n}\nexport { tsRestType as tSRestType };\nexport function tsNamedTupleMember(\n  label: t.Identifier,\n  elementType: t.TSType,\n  optional: boolean = false,\n): t.TSNamedTupleMember {\n  return validateNode<t.TSNamedTupleMember>({\n    type: \"TSNamedTupleMember\",\n    label,\n    elementType,\n    optional,\n  });\n}\nexport { tsNamedTupleMember as tSNamedTupleMember };\nexport function tsUnionType(types: Array<t.TSType>): t.TSUnionType {\n  return validateNode<t.TSUnionType>({\n    type: \"TSUnionType\",\n    types,\n  });\n}\nexport { tsUnionType as tSUnionType };\nexport function tsIntersectionType(\n  types: Array<t.TSType>,\n): t.TSIntersectionType {\n  return validateNode<t.TSIntersectionType>({\n    type: \"TSIntersectionType\",\n    types,\n  });\n}\nexport { tsIntersectionType as tSIntersectionType };\nexport function tsConditionalType(\n  checkType: t.TSType,\n  extendsType: t.TSType,\n  trueType: t.TSType,\n  falseType: t.TSType,\n): t.TSConditionalType {\n  return validateNode<t.TSConditionalType>({\n    type: \"TSConditionalType\",\n    checkType,\n    extendsType,\n    trueType,\n    falseType,\n  });\n}\nexport { tsConditionalType as tSConditionalType };\nexport function tsInferType(typeParameter: t.TSTypeParameter): t.TSInferType {\n  return validateNode<t.TSInferType>({\n    type: \"TSInferType\",\n    typeParameter,\n  });\n}\nexport { tsInferType as tSInferType };\nexport function tsParenthesizedType(\n  typeAnnotation: t.TSType,\n): t.TSParenthesizedType {\n  return validateNode<t.TSParenthesizedType>({\n    type: \"TSParenthesizedType\",\n    typeAnnotation,\n  });\n}\nexport { tsParenthesizedType as tSParenthesizedType };\nexport function tsTypeOperator(typeAnnotation: t.TSType): t.TSTypeOperator {\n  return validateNode<t.TSTypeOperator>({\n    type: \"TSTypeOperator\",\n    typeAnnotation,\n    operator: null,\n  });\n}\nexport { tsTypeOperator as tSTypeOperator };\nexport function tsIndexedAccessType(\n  objectType: t.TSType,\n  indexType: t.TSType,\n): t.TSIndexedAccessType {\n  return validateNode<t.TSIndexedAccessType>({\n    type: \"TSIndexedAccessType\",\n    objectType,\n    indexType,\n  });\n}\nexport { tsIndexedAccessType as tSIndexedAccessType };\nexport function tsMappedType(\n  typeParameter: t.TSTypeParameter,\n  typeAnnotation: t.TSType | null = null,\n  nameType: t.TSType | null = null,\n): t.TSMappedType {\n  return validateNode<t.TSMappedType>({\n    type: \"TSMappedType\",\n    typeParameter,\n    typeAnnotation,\n    nameType,\n  });\n}\nexport { tsMappedType as tSMappedType };\nexport function tsLiteralType(\n  literal:\n    | t.NumericLiteral\n    | t.StringLiteral\n    | t.BooleanLiteral\n    | t.BigIntLiteral\n    | t.TemplateLiteral\n    | t.UnaryExpression,\n): t.TSLiteralType {\n  return validateNode<t.TSLiteralType>({\n    type: \"TSLiteralType\",\n    literal,\n  });\n}\nexport { tsLiteralType as tSLiteralType };\nexport function tsExpressionWithTypeArguments(\n  expression: t.TSEntityName,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSExpressionWithTypeArguments {\n  return validateNode<t.TSExpressionWithTypeArguments>({\n    type: \"TSExpressionWithTypeArguments\",\n    expression,\n    typeParameters,\n  });\n}\nexport { tsExpressionWithTypeArguments as tSExpressionWithTypeArguments };\nexport function tsInterfaceDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.TSExpressionWithTypeArguments> | null | undefined = null,\n  body: t.TSInterfaceBody,\n): t.TSInterfaceDeclaration {\n  return validateNode<t.TSInterfaceDeclaration>({\n    type: \"TSInterfaceDeclaration\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport { tsInterfaceDeclaration as tSInterfaceDeclaration };\nexport function tsInterfaceBody(\n  body: Array<t.TSTypeElement>,\n): t.TSInterfaceBody {\n  return validateNode<t.TSInterfaceBody>({\n    type: \"TSInterfaceBody\",\n    body,\n  });\n}\nexport { tsInterfaceBody as tSInterfaceBody };\nexport function tsTypeAliasDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  typeAnnotation: t.TSType,\n): t.TSTypeAliasDeclaration {\n  return validateNode<t.TSTypeAliasDeclaration>({\n    type: \"TSTypeAliasDeclaration\",\n    id,\n    typeParameters,\n    typeAnnotation,\n  });\n}\nexport { tsTypeAliasDeclaration as tSTypeAliasDeclaration };\nexport function tsInstantiationExpression(\n  expression: t.Expression,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSInstantiationExpression {\n  return validateNode<t.TSInstantiationExpression>({\n    type: \"TSInstantiationExpression\",\n    expression,\n    typeParameters,\n  });\n}\nexport { tsInstantiationExpression as tSInstantiationExpression };\nexport function tsAsExpression(\n  expression: t.Expression,\n  typeAnnotation: t.TSType,\n): t.TSAsExpression {\n  return validateNode<t.TSAsExpression>({\n    type: \"TSAsExpression\",\n    expression,\n    typeAnnotation,\n  });\n}\nexport { tsAsExpression as tSAsExpression };\nexport function tsTypeAssertion(\n  typeAnnotation: t.TSType,\n  expression: t.Expression,\n): t.TSTypeAssertion {\n  return validateNode<t.TSTypeAssertion>({\n    type: \"TSTypeAssertion\",\n    typeAnnotation,\n    expression,\n  });\n}\nexport { tsTypeAssertion as tSTypeAssertion };\nexport function tsEnumDeclaration(\n  id: t.Identifier,\n  members: Array<t.TSEnumMember>,\n): t.TSEnumDeclaration {\n  return validateNode<t.TSEnumDeclaration>({\n    type: \"TSEnumDeclaration\",\n    id,\n    members,\n  });\n}\nexport { tsEnumDeclaration as tSEnumDeclaration };\nexport function tsEnumMember(\n  id: t.Identifier | t.StringLiteral,\n  initializer: t.Expression | null = null,\n): t.TSEnumMember {\n  return validateNode<t.TSEnumMember>({\n    type: \"TSEnumMember\",\n    id,\n    initializer,\n  });\n}\nexport { tsEnumMember as tSEnumMember };\nexport function tsModuleDeclaration(\n  id: t.Identifier | t.StringLiteral,\n  body: t.TSModuleBlock | t.TSModuleDeclaration,\n): t.TSModuleDeclaration {\n  return validateNode<t.TSModuleDeclaration>({\n    type: \"TSModuleDeclaration\",\n    id,\n    body,\n  });\n}\nexport { tsModuleDeclaration as tSModuleDeclaration };\nexport function tsModuleBlock(body: Array<t.Statement>): t.TSModuleBlock {\n  return validateNode<t.TSModuleBlock>({\n    type: \"TSModuleBlock\",\n    body,\n  });\n}\nexport { tsModuleBlock as tSModuleBlock };\nexport function tsImportType(\n  argument: t.StringLiteral,\n  qualifier: t.TSEntityName | null = null,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSImportType {\n  return validateNode<t.TSImportType>({\n    type: \"TSImportType\",\n    argument,\n    qualifier,\n    typeParameters,\n  });\n}\nexport { tsImportType as tSImportType };\nexport function tsImportEqualsDeclaration(\n  id: t.Identifier,\n  moduleReference: t.TSEntityName | t.TSExternalModuleReference,\n): t.TSImportEqualsDeclaration {\n  return validateNode<t.TSImportEqualsDeclaration>({\n    type: \"TSImportEqualsDeclaration\",\n    id,\n    moduleReference,\n    isExport: null,\n  });\n}\nexport { tsImportEqualsDeclaration as tSImportEqualsDeclaration };\nexport function tsExternalModuleReference(\n  expression: t.StringLiteral,\n): t.TSExternalModuleReference {\n  return validateNode<t.TSExternalModuleReference>({\n    type: \"TSExternalModuleReference\",\n    expression,\n  });\n}\nexport { tsExternalModuleReference as tSExternalModuleReference };\nexport function tsNonNullExpression(\n  expression: t.Expression,\n): t.TSNonNullExpression {\n  return validateNode<t.TSNonNullExpression>({\n    type: \"TSNonNullExpression\",\n    expression,\n  });\n}\nexport { tsNonNullExpression as tSNonNullExpression };\nexport function tsExportAssignment(\n  expression: t.Expression,\n): t.TSExportAssignment {\n  return validateNode<t.TSExportAssignment>({\n    type: \"TSExportAssignment\",\n    expression,\n  });\n}\nexport { tsExportAssignment as tSExportAssignment };\nexport function tsNamespaceExportDeclaration(\n  id: t.Identifier,\n): t.TSNamespaceExportDeclaration {\n  return validateNode<t.TSNamespaceExportDeclaration>({\n    type: \"TSNamespaceExportDeclaration\",\n    id,\n  });\n}\nexport { tsNamespaceExportDeclaration as tSNamespaceExportDeclaration };\nexport function tsTypeAnnotation(typeAnnotation: t.TSType): t.TSTypeAnnotation {\n  return validateNode<t.TSTypeAnnotation>({\n    type: \"TSTypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport { tsTypeAnnotation as tSTypeAnnotation };\nexport function tsTypeParameterInstantiation(\n  params: Array<t.TSType>,\n): t.TSTypeParameterInstantiation {\n  return validateNode<t.TSTypeParameterInstantiation>({\n    type: \"TSTypeParameterInstantiation\",\n    params,\n  });\n}\nexport { tsTypeParameterInstantiation as tSTypeParameterInstantiation };\nexport function tsTypeParameterDeclaration(\n  params: Array<t.TSTypeParameter>,\n): t.TSTypeParameterDeclaration {\n  return validateNode<t.TSTypeParameterDeclaration>({\n    type: \"TSTypeParameterDeclaration\",\n    params,\n  });\n}\nexport { tsTypeParameterDeclaration as tSTypeParameterDeclaration };\nexport function tsTypeParameter(\n  constraint: t.TSType | null | undefined = null,\n  _default: t.TSType | null | undefined = null,\n  name: string,\n): t.TSTypeParameter {\n  return validateNode<t.TSTypeParameter>({\n    type: \"TSTypeParameter\",\n    constraint,\n    default: _default,\n    name,\n  });\n}\nexport { tsTypeParameter as tSTypeParameter };\n/** @deprecated */\nfunction NumberLiteral(value: number) {\n  console.trace(\n    \"The node type NumberLiteral has been renamed to NumericLiteral\",\n  );\n  return numericLiteral(value);\n}\nexport { NumberLiteral as numberLiteral };\n/** @deprecated */\nfunction RegexLiteral(pattern: string, flags: string = \"\") {\n  console.trace(\"The node type RegexLiteral has been renamed to RegExpLiteral\");\n  return regExpLiteral(pattern, flags);\n}\nexport { RegexLiteral as regexLiteral };\n/** @deprecated */\nfunction RestProperty(argument: t.LVal) {\n  console.trace(\"The node type RestProperty has been renamed to RestElement\");\n  return restElement(argument);\n}\nexport { RestProperty as restProperty };\n/** @deprecated */\nfunction SpreadProperty(argument: t.Expression) {\n  console.trace(\n    \"The node type SpreadProperty has been renamed to SpreadElement\",\n  );\n  return spreadElement(argument);\n}\nexport { SpreadProperty as spreadProperty };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;;AAEO,SAASA,eAAT,CACLC,QAAsD,GAAG,EADpD,EAEc;EACnB,OAAO,IAAAC,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCF;EAFqC,CAAhC,CAAP;AAID;;AACM,SAASG,oBAAT,CACLC,QADK,EAELC,IAFK,EAGLC,KAHK,EAImB;EACxB,OAAO,IAAAL,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CE,QAF0C;IAG1CC,IAH0C;IAI1CC;EAJ0C,CAArC,CAAP;AAMD;;AACM,SAASC,gBAAT,CACLH,QADK,EAyBLC,IAzBK,EA0BLC,KA1BK,EA2Be;EACpB,OAAO,IAAAL,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCE,QAFsC;IAGtCC,IAHsC;IAItCC;EAJsC,CAAjC,CAAP;AAMD;;AACM,SAASE,oBAAT,CAA8BC,KAA9B,EAAqE;EAC1E,OAAO,IAAAR,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CO;EAF0C,CAArC,CAAP;AAID;;AACM,SAASC,SAAT,CAAmBD,KAAnB,EAA2D;EAChE,OAAO,IAAAR,qBAAA,EAA0B;IAC/BC,IAAI,EAAE,WADyB;IAE/BO;EAF+B,CAA1B,CAAP;AAID;;AACM,SAASE,gBAAT,CAA0BF,KAA1B,EAA6D;EAClE,OAAO,IAAAR,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCO;EAFsC,CAAjC,CAAP;AAID;;AACM,SAASG,cAAT,CACLC,IADK,EAELC,UAA8B,GAAG,EAF5B,EAGa;EAClB,OAAO,IAAAb,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCW,IAFoC;IAGpCC;EAHoC,CAA/B,CAAP;AAKD;;AACM,SAASC,cAAT,CACLC,KAA0B,GAAG,IADxB,EAEa;EAClB,OAAO,IAAAf,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCc;EAFoC,CAA/B,CAAP;AAID;;AACM,SAASC,cAAT,CACLC,MADK,EAELC,UAFK,EAKa;EAClB,OAAO,IAAAlB,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCgB,MAFoC;IAGpCE,SAAS,EAAED;EAHyB,CAA/B,CAAP;AAKD;;AACM,SAASE,WAAT,CACLC,KAKa,GAAG,IANX,EAOLT,IAPK,EAQU;EACf,OAAO,IAAAZ,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCoB,KAFiC;IAGjCT;EAHiC,CAA5B,CAAP;AAKD;;AACM,SAASU,qBAAT,CACLC,IADK,EAELC,UAFK,EAGLC,SAHK,EAIoB;EACzB,OAAO,IAAAzB,qBAAA,EAAsC;IAC3CC,IAAI,EAAE,uBADqC;IAE3CsB,IAF2C;IAG3CC,UAH2C;IAI3CC;EAJ2C,CAAtC,CAAP;AAMD;;AACM,SAASC,iBAAT,CACLX,KAA0B,GAAG,IADxB,EAEgB;EACrB,OAAO,IAAAf,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCc;EAFuC,CAAlC,CAAP;AAID;;AACM,SAASY,iBAAT,GAAkD;EACvD,OAAO;IACL1B,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS2B,gBAAT,CACLL,IADK,EAELX,IAFK,EAGe;EACpB,OAAO,IAAAZ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCsB,IAFsC;IAGtCX;EAHsC,CAAjC,CAAP;AAKD;;AACM,SAASiB,cAAT,GAA4C;EACjD,OAAO;IACL5B,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS6B,mBAAT,CACLC,UADK,EAEkB;EACvB,OAAO,IAAA/B,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC8B;EAFyC,CAApC,CAAP;AAID;;AACM,SAASC,IAAT,CACLC,OADK,EAELC,QAAsD,GAAG,IAFpD,EAGLC,MAAyB,GAAG,IAHvB,EAIG;EACR,OAAO,IAAAnC,qBAAA,EAAqB;IAC1BC,IAAI,EAAE,MADoB;IAE1BgC,OAF0B;IAG1BC,QAH0B;IAI1BC;EAJ0B,CAArB,CAAP;AAMD;;AACM,SAASC,cAAT,CACLhC,IADK,EAELC,KAFK,EAGLO,IAHK,EAIa;EAClB,OAAO,IAAAZ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCG,IAFoC;IAGpCC,KAHoC;IAIpCO;EAJoC,CAA/B,CAAP;AAMD;;AACM,SAASyB,YAAT,CACLC,IAA6D,GAAG,IAD3D,EAELf,IAAqC,GAAG,IAFnC,EAGLgB,MAAuC,GAAG,IAHrC,EAIL3B,IAJK,EAKW;EAChB,OAAO,IAAAZ,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCqC,IAFkC;IAGlCf,IAHkC;IAIlCgB,MAJkC;IAKlC3B;EALkC,CAA7B,CAAP;AAOD;;AACM,SAAS4B,mBAAT,CACLC,EAAmC,GAAG,IADjC,EAELC,MAFK,EAGL9B,IAHK,EAIL+B,SAAkB,GAAG,KAJhB,EAKLC,KAAc,GAAG,KALZ,EAMkB;EACvB,OAAO,IAAA5C,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCwC,EAFyC;IAGzCC,MAHyC;IAIzC9B,IAJyC;IAKzC+B,SALyC;IAMzCC;EANyC,CAApC,CAAP;AAQD;;AACM,SAASC,kBAAT,CACLJ,EAAmC,GAAG,IADjC,EAELC,MAFK,EAGL9B,IAHK,EAIL+B,SAAkB,GAAG,KAJhB,EAKLC,KAAc,GAAG,KALZ,EAMiB;EACtB,OAAO,IAAA5C,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCwC,EAFwC;IAGxCC,MAHwC;IAIxC9B,IAJwC;IAKxC+B,SALwC;IAMxCC;EANwC,CAAnC,CAAP;AAQD;;AACM,SAASE,UAAT,CAAoBC,IAApB,EAAgD;EACrD,OAAO,IAAA/C,qBAAA,EAA2B;IAChCC,IAAI,EAAE,YAD0B;IAEhC8C;EAFgC,CAA3B,CAAP;AAID;;AACM,SAASC,WAAT,CACLzB,IADK,EAELC,UAFK,EAGLC,SAA6B,GAAG,IAH3B,EAIU;EACf,OAAO,IAAAzB,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCsB,IAFiC;IAGjCC,UAHiC;IAIjCC;EAJiC,CAA5B,CAAP;AAMD;;AACM,SAASwB,gBAAT,CACLlC,KADK,EAELH,IAFK,EAGe;EACpB,OAAO,IAAAZ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCc,KAFsC;IAGtCH;EAHsC,CAAjC,CAAP;AAKD;;AACM,SAASsC,aAAT,CAAuB1C,KAAvB,EAAuD;EAC5D,OAAO,IAAAR,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCO;EAFmC,CAA9B,CAAP;AAID;;AACM,SAAS2C,cAAT,CAAwB3C,KAAxB,EAAyD;EAC9D,OAAO,IAAAR,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCO;EAFoC,CAA/B,CAAP;AAID;;AACM,SAAS4C,WAAT,GAAsC;EAC3C,OAAO;IACLnD,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASoD,cAAT,CAAwB7C,KAAxB,EAA0D;EAC/D,OAAO,IAAAR,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCO;EAFoC,CAA/B,CAAP;AAID;;AACM,SAAS8C,aAAT,CACLC,OADK,EAELC,KAAa,GAAG,EAFX,EAGY;EACjB,OAAO,IAAAxD,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCsD,OAFmC;IAGnCC;EAHmC,CAA9B,CAAP;AAKD;;AACM,SAASC,iBAAT,CACLtD,QADK,EAELC,IAFK,EAGLC,KAHK,EAIgB;EACrB,OAAO,IAAAL,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCE,QAFuC;IAGvCC,IAHuC;IAIvCC;EAJuC,CAAlC,CAAP;AAMD;;AACM,SAASqD,gBAAT,CACLC,MADK,EAELC,QAFK,EAGLC,QAAiB,GAAG,KAHf,EAILC,QAA6B,GAAG,IAJ3B,EAKe;EACpB,OAAO,IAAA9D,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtC0D,MAFsC;IAGtCC,QAHsC;IAItCC,QAJsC;IAKtCC;EALsC,CAAjC,CAAP;AAOD;;AACM,SAASC,aAAT,CACL9C,MADK,EAELC,UAFK,EAKY;EACjB,OAAO,IAAAlB,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCgB,MAFmC;IAGnCE,SAAS,EAAED;EAHwB,CAA9B,CAAP;AAKD;;AACM,SAASe,OAAT,CACLrB,IADK,EAELC,UAA8B,GAAG,EAF5B,EAGLmD,UAA+B,GAAG,QAH7B,EAILC,WAA0C,GAAG,IAJxC,EAKM;EACX,OAAO,IAAAjE,qBAAA,EAAwB;IAC7BC,IAAI,EAAE,SADuB;IAE7BW,IAF6B;IAG7BC,UAH6B;IAI7BmD,UAJ6B;IAK7BC,WAL6B;IAM7BC,UAAU,EAAE;EANiB,CAAxB,CAAP;AAQD;;AACM,SAASC,gBAAT,CACLC,UADK,EAEe;EACpB,OAAO,IAAApE,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCmE;EAFsC,CAAjC,CAAP;AAID;;AACM,SAASC,YAAT,CACLC,IAA0C,GAAG,QADxC,EAELC,GAFK,EAQL7B,MARK,EASL9B,IATK,EAULiD,QAAiB,GAAG,KAVf,EAWLlB,SAAkB,GAAG,KAXhB,EAYLC,KAAc,GAAG,KAZZ,EAaW;EAChB,OAAO,IAAA5C,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCqE,IAFkC;IAGlCC,GAHkC;IAIlC7B,MAJkC;IAKlC9B,IALkC;IAMlCiD,QANkC;IAOlClB,SAPkC;IAQlCC;EARkC,CAA7B,CAAP;AAUD;;AACM,SAAS4B,cAAT,CACLD,GADK,EASL/D,KATK,EAULqD,QAAiB,GAAG,KAVf,EAWLY,SAAkB,GAAG,KAXhB,EAYLC,UAAqC,GAAG,IAZnC,EAaa;EAClB,OAAO,IAAA1E,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCsE,GAFoC;IAGpC/D,KAHoC;IAIpCqD,QAJoC;IAKpCY,SALoC;IAMpCC;EANoC,CAA/B,CAAP;AAQD;;AACM,SAASC,WAAT,CAAqBC,QAArB,EAAsD;EAC3D,OAAO,IAAA5E,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjC2E;EAFiC,CAA5B,CAAP;AAID;;AACM,SAASC,eAAT,CACLD,QAA6B,GAAG,IAD3B,EAEc;EACnB,OAAO,IAAA5E,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErC2E;EAFqC,CAAhC,CAAP;AAID;;AACM,SAASE,kBAAT,CACLC,WADK,EAEiB;EACtB,OAAO,IAAA/E,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExC8E;EAFwC,CAAnC,CAAP;AAID;;AACM,SAASC,uBAAT,CACLjD,UADK,EAEsB;EAC3B,OAAO,IAAA/B,qBAAA,EAAwC;IAC7CC,IAAI,EAAE,yBADuC;IAE7C8B;EAF6C,CAAxC,CAAP;AAID;;AACM,SAASkD,UAAT,CACL1D,IAAqC,GAAG,IADnC,EAELC,UAFK,EAGS;EACd,OAAO,IAAAxB,qBAAA,EAA2B;IAChCC,IAAI,EAAE,YAD0B;IAEhCsB,IAFgC;IAGhCC;EAHgC,CAA3B,CAAP;AAKD;;AACM,SAAS0D,eAAT,CACLC,YADK,EAELC,KAFK,EAGc;EACnB,OAAO,IAAApF,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCkF,YAFqC;IAGrCC;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,cAAT,GAA4C;EACjD,OAAO;IACLpF,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASqF,cAAT,CAAwBV,QAAxB,EAAkE;EACvE,OAAO,IAAA5E,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpC2E;EAFoC,CAA/B,CAAP;AAID;;AACM,SAASW,YAAT,CACLC,KADK,EAELC,OAA6B,GAAG,IAF3B,EAGLC,SAAkC,GAAG,IAHhC,EAIW;EAChB,OAAO,IAAA1F,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCuF,KAFkC;IAGlCC,OAHkC;IAIlCC;EAJkC,CAA7B,CAAP;AAMD;;AACM,SAASC,eAAT,CACLxF,QADK,EAELyE,QAFK,EAGLgB,MAAe,GAAG,IAHb,EAIc;EACnB,OAAO,IAAA5F,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCE,QAFqC;IAGrCyE,QAHqC;IAIrCgB;EAJqC,CAAhC,CAAP;AAMD;;AACM,SAASC,gBAAT,CACL1F,QADK,EAELyE,QAFK,EAGLgB,MAAe,GAAG,KAHb,EAIe;EACpB,OAAO,IAAA5F,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCE,QAFsC;IAGtCyE,QAHsC;IAItCgB;EAJsC,CAAjC,CAAP;AAMD;;AACM,SAASE,mBAAT,CACLxB,IADK,EAELyB,YAFK,EAGkB;EACvB,OAAO,IAAA/F,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCqE,IAFyC;IAGzCyB;EAHyC,CAApC,CAAP;AAKD;;AACM,SAASC,kBAAT,CACLvD,EADK,EAELH,IAAyB,GAAG,IAFvB,EAGiB;EACtB,OAAO,IAAAtC,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCwC,EAFwC;IAGxCH;EAHwC,CAAnC,CAAP;AAKD;;AACM,SAAS2D,cAAT,CACL1E,IADK,EAELX,IAFK,EAGa;EAClB,OAAO,IAAAZ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCsB,IAFoC;IAGpCX;EAHoC,CAA/B,CAAP;AAKD;;AACM,SAASsF,aAAT,CACLvC,MADK,EAEL/C,IAFK,EAGY;EACjB,OAAO,IAAAZ,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnC0D,MAFmC;IAGnC/C;EAHmC,CAA9B,CAAP;AAKD;;AACM,SAASuF,iBAAT,CACL/F,IADK,EASLC,KATK,EAUgB;EACrB,OAAO,IAAAL,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCG,IAFuC;IAGvCC;EAHuC,CAAlC,CAAP;AAKD;;AACM,SAAS+F,YAAT,CACLrG,QADK,EAEW;EAChB,OAAO,IAAAC,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCF;EAFkC,CAA7B,CAAP;AAID;;AACM,SAASsG,uBAAT,CACL3D,MADK,EAEL9B,IAFK,EAGLgC,KAAc,GAAG,KAHZ,EAIsB;EAC3B,OAAO,IAAA5C,qBAAA,EAAwC;IAC7CC,IAAI,EAAE,yBADuC;IAE7CyC,MAF6C;IAG7C9B,IAH6C;IAI7CgC,KAJ6C;IAK7Cb,UAAU,EAAE;EALiC,CAAxC,CAAP;AAOD;;AACM,SAASuE,SAAT,CACL1F,IADK,EAWQ;EACb,OAAO,IAAAZ,qBAAA,EAA0B;IAC/BC,IAAI,EAAE,WADyB;IAE/BW;EAF+B,CAA1B,CAAP;AAID;;AACM,SAAS2F,eAAT,CACL9D,EAAmC,GAAG,IADjC,EAEL+D,UAA2C,GAAG,IAFzC,EAGL5F,IAHK,EAIL8D,UAAqC,GAAG,IAJnC,EAKc;EACnB,OAAO,IAAA1E,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwC,EAFqC;IAGrC+D,UAHqC;IAIrC5F,IAJqC;IAKrC8D;EALqC,CAAhC,CAAP;AAOD;;AACM,SAAS+B,gBAAT,CACLhE,EADK,EAEL+D,UAA2C,GAAG,IAFzC,EAGL5F,IAHK,EAIL8D,UAAqC,GAAG,IAJnC,EAKe;EACpB,OAAO,IAAA1E,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtC+D,UAHsC;IAItC5F,IAJsC;IAKtC8D;EALsC,CAAjC,CAAP;AAOD;;AACM,SAASgC,oBAAT,CACLC,MADK,EAEmB;EACxB,OAAO,IAAA3G,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1C0G;EAF0C,CAArC,CAAP;AAID;;AACM,SAASC,wBAAT,CACLC,WADK,EAMuB;EAC5B,OAAO,IAAA7G,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9C4G;EAF8C,CAAzC,CAAP;AAID;;AACM,SAASC,sBAAT,CACLD,WAAiC,GAAG,IAD/B,EAELE,UAEC,GAAG,EAJC,EAKLJ,MAA8B,GAAG,IAL5B,EAMqB;EAC1B,OAAO,IAAA3G,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5C4G,WAF4C;IAG5CE,UAH4C;IAI5CJ;EAJ4C,CAAvC,CAAP;AAMD;;AACM,SAASK,eAAT,CACLC,KADK,EAELC,QAFK,EAGc;EACnB,OAAO,IAAAlH,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCgH,KAFqC;IAGrCC;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,cAAT,CACL/G,IADK,EAELC,KAFK,EAGLO,IAHK,EAILwG,MAAe,GAAG,KAJb,EAKa;EAClB,OAAO,IAAApH,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCG,IAFoC;IAGpCC,KAHoC;IAIpCO,IAJoC;IAKpCyG,KAAK,EAAED;EAL6B,CAA/B,CAAP;AAOD;;AACM,SAASE,iBAAT,CACLP,UADK,EAILJ,MAJK,EAKgB;EACrB,OAAO,IAAA3G,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC8G,UAFuC;IAGvCJ;EAHuC,CAAlC,CAAP;AAKD;;AACM,SAASY,sBAAT,CACLN,KADK,EAEqB;EAC1B,OAAO,IAAAjH,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CgH;EAF4C,CAAvC,CAAP;AAID;;AACM,SAASO,wBAAT,CACLP,KADK,EAEuB;EAC5B,OAAO,IAAAjH,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9CgH;EAF8C,CAAzC,CAAP;AAID;;AACM,SAASQ,eAAT,CACLR,KADK,EAELS,QAFK,EAGc;EACnB,OAAO,IAAA1H,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCgH,KAFqC;IAGrCS;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,YAAT,CACLC,IADK,EAELhE,QAFK,EAGW;EAChB,OAAO,IAAA5D,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElC2H,IAFkC;IAGlChE;EAHkC,CAA7B,CAAP;AAKD;;AACM,SAASiE,WAAT,CACLvD,IAA0D,GAAG,QADxD,EAELC,GAFK,EAQL7B,MARK,EAWL9B,IAXK,EAYLiD,QAAiB,GAAG,KAZf,EAaLiE,OAAgB,GAAG,KAbd,EAcLnF,SAAkB,GAAG,KAdhB,EAeLC,KAAc,GAAG,KAfZ,EAgBU;EACf,OAAO,IAAA5C,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCqE,IAFiC;IAGjCC,GAHiC;IAIjC7B,MAJiC;IAKjC9B,IALiC;IAMjCiD,QANiC;IAOjCkE,MAAM,EAAED,OAPyB;IAQjCnF,SARiC;IASjCC;EATiC,CAA5B,CAAP;AAWD;;AACM,SAASoF,aAAT,CACL5D,UADK,EAEY;EACjB,OAAO,IAAApE,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCmE;EAFmC,CAA9B,CAAP;AAID;;AACM,SAAS6D,aAAT,CAAuBrD,QAAvB,EAAgE;EACrE,OAAO,IAAA5E,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnC2E;EAFmC,CAA9B,CAAP;AAID;;AACD,SAASsD,MAAT,GAA2B;EACzB,OAAO;IACLjI,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASkI,wBAAT,CACLC,GADK,EAELC,KAFK,EAGuB;EAC5B,OAAO,IAAArI,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9CmI,GAF8C;IAG9CC;EAH8C,CAAzC,CAAP;AAKD;;AACM,SAASC,eAAT,CACL9H,KADK,EAEL+H,IAAa,GAAG,KAFX,EAGc;EACnB,OAAO,IAAAvI,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCO,KAFqC;IAGrC+H;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,eAAT,CACLC,MADK,EAEL1D,WAFK,EAGc;EACnB,OAAO,IAAA/E,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwI,MAFqC;IAGrC1D;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAAS2D,eAAT,CACL9D,QAA6B,GAAG,IAD3B,EAEL+D,QAAiB,GAAG,KAFf,EAGc;EACnB,OAAO,IAAA3I,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErC2E,QAFqC;IAGrC+D;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,eAAT,CAAyBhE,QAAzB,EAAoE;EACzE,OAAO,IAAA5E,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErC2E;EAFqC,CAAhC,CAAP;AAID;;AACD,SAASiE,OAAT,GAA6B;EAC3B,OAAO;IACL5I,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS6I,aAAT,CAAuBtI,KAAvB,EAAuD;EAC5D,OAAO,IAAAR,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCO;EAFmC,CAA9B,CAAP;AAID;;AACM,SAASuI,wBAAT,CACL7B,QADK,EAEuB;EAC5B,OAAO,IAAAlH,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9CiH;EAF8C,CAAzC,CAAP;AAID;;AACM,SAAS8B,wBAAT,CACLrF,MADK,EAELC,QAFK,EAGLC,QAA6B,GAAG,KAH3B,EAILC,QAJK,EAKuB;EAC5B,OAAO,IAAA9D,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9C0D,MAF8C;IAG9CC,QAH8C;IAI9CC,QAJ8C;IAK9CC;EAL8C,CAAzC,CAAP;AAOD;;AACM,SAASmF,sBAAT,CACLhI,MADK,EAELC,UAFK,EAKL4C,QALK,EAMqB;EAC1B,OAAO,IAAA9D,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CgB,MAF4C;IAG5CE,SAAS,EAAED,UAHiC;IAI5C4C;EAJ4C,CAAvC,CAAP;AAMD;;AACM,SAASoF,aAAT,CACL3E,GADK,EAOL/D,KAA0B,GAAG,IAPxB,EAQL2I,cAAqE,GAAG,IARnE,EASLzE,UAAqC,GAAG,IATnC,EAULb,QAAiB,GAAG,KAVf,EAWLiE,OAAgB,GAAG,KAXd,EAYY;EACjB,OAAO,IAAA9H,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCsE,GAFmC;IAGnC/D,KAHmC;IAInC2I,cAJmC;IAKnCzE,UALmC;IAMnCb,QANmC;IAOnCkE,MAAM,EAAED;EAP2B,CAA9B,CAAP;AASD;;AACM,SAASsB,qBAAT,CACL7E,GADK,EAQL/D,KAA0B,GAAG,IARxB,EASL2I,cAAqE,GAAG,IATnE,EAULzE,UAAqC,GAAG,IAVnC,EAWLb,QAAiB,GAAG,KAXf,EAYLiE,OAAgB,GAAG,KAZd,EAaoB;EACzB,OAAO,IAAA9H,qBAAA,EAAsC;IAC3CC,IAAI,EAAE,uBADqC;IAE3CsE,GAF2C;IAG3C/D,KAH2C;IAI3C2I,cAJ2C;IAK3CzE,UAL2C;IAM3Cb,QAN2C;IAO3CkE,MAAM,EAAED;EAPmC,CAAtC,CAAP;AASD;;AACM,SAASuB,oBAAT,CACL9E,GADK,EAEL/D,KAA0B,GAAG,IAFxB,EAGLkE,UAAqC,GAAG,IAHnC,EAILoD,OAAgB,GAAG,KAJd,EAKmB;EACxB,OAAO,IAAA9H,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CsE,GAF0C;IAG1C/D,KAH0C;IAI1CkE,UAJ0C;IAK1CqD,MAAM,EAAED;EALkC,CAArC,CAAP;AAOD;;AACM,SAASwB,kBAAT,CACLhF,IAA0C,GAAG,QADxC,EAELC,GAFK,EAGL7B,MAHK,EAML9B,IANK,EAOLkH,OAAgB,GAAG,KAPd,EAQiB;EACtB,OAAO,IAAA9H,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCqE,IAFwC;IAGxCC,GAHwC;IAIxC7B,MAJwC;IAKxC9B,IALwC;IAMxCmH,MAAM,EAAED;EANgC,CAAnC,CAAP;AAQD;;AACM,SAASyB,WAAT,CAAqB9G,EAArB,EAAsD;EAC3D,OAAO,IAAAzC,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCwC;EAFiC,CAA5B,CAAP;AAID;;AACM,SAAS+G,WAAT,CAAqB5I,IAArB,EAA8D;EACnE,OAAO,IAAAZ,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCW;EAFiC,CAA5B,CAAP;AAID;;AACM,SAAS6I,iBAAT,GAAkD;EACvD,OAAO;IACLxJ,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASyJ,mBAAT,CACLC,WADK,EAEkB;EACvB,OAAO,IAAA3J,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC0J;EAFyC,CAApC,CAAP;AAID;;AACM,SAASC,qBAAT,GAA0D;EAC/D,OAAO;IACL3J,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS4J,4BAAT,CACLrJ,KADK,EAE2B;EAChC,OAAO,IAAAR,qBAAA,EAA6C;IAClDC,IAAI,EAAE,8BAD4C;IAElDO;EAFkD,CAA7C,CAAP;AAID;;AACM,SAASsJ,yBAAT,GAAkE;EACvE,OAAO;IACL7J,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS8J,eAAT,CACLtH,EADK,EAELuH,cAAmD,GAAG,IAFjD,EAGc;EACnB,OAAO,IAAAhK,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwC,EAFqC;IAGrCuH;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASC,YAAT,CACLxH,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGLE,QAAsD,GAAG,IAHpD,EAILtJ,IAJK,EAKW;EAChB,OAAO,IAAAZ,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCwC,EAFkC;IAGlCuH,cAHkC;IAIlCG,OAAO,EAAED,QAJyB;IAKlCtJ;EALkC,CAA7B,CAAP;AAOD;;AACM,SAASwJ,eAAT,CAAyB3H,EAAzB,EAA8D;EACnE,OAAO,IAAAzC,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwC;EAFqC,CAAhC,CAAP;AAID;;AACM,SAAS4H,gBAAT,CACL5H,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGLE,QAAsD,GAAG,IAHpD,EAILtJ,IAJK,EAKe;EACpB,OAAO,IAAAZ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtCuH,cAHsC;IAItCG,OAAO,EAAED,QAJ6B;IAKtCtJ;EALsC,CAAjC,CAAP;AAOD;;AACM,SAAS0J,aAAT,CACL7H,EADK,EAEL7B,IAFK,EAGL0D,IAA8B,GAAG,IAH5B,EAIY;EACjB,OAAO,IAAAtE,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCwC,EAFmC;IAGnC7B,IAHmC;IAInC0D;EAJmC,CAA9B,CAAP;AAMD;;AACM,SAASiG,oBAAT,CACLpB,cADK,EAEmB;EACxB,OAAO,IAAAnJ,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CkJ;EAF0C,CAArC,CAAP;AAID;;AACM,SAASqB,gBAAT,CACL/H,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGL3J,KAHK,EAIe;EACpB,OAAO,IAAAL,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtCuH,cAHsC;IAItC3J;EAJsC,CAAjC,CAAP;AAMD;;AACM,SAASoK,iBAAT,CACLhI,EADK,EAELuH,cAAiD,GAAG,IAF/C,EAGLU,SAA4B,GAAG,IAH1B,EAIgB;EACrB,OAAO,IAAA1K,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCwC,EAFuC;IAGvCuH,cAHuC;IAIvCU;EAJuC,CAAlC,CAAP;AAMD;;AACM,SAASC,eAAT,CAAyBlI,EAAzB,EAA8D;EACnE,OAAO,IAAAzC,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwC;EAFqC,CAAhC,CAAP;AAID;;AACM,SAASmI,wBAAT,CACL/D,WAA0B,GAAG,IADxB,EAELE,UAEQ,GAAG,IAJN,EAKLJ,MAA8B,GAAG,IAL5B,EAMuB;EAC5B,OAAO,IAAA3G,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9C4G,WAF8C;IAG9CE,UAH8C;IAI9CJ;EAJ8C,CAAzC,CAAP;AAMD;;AACM,SAASkE,2BAAT,CACLlE,MADK,EAE0B;EAC/B,OAAO,IAAA3G,qBAAA,EAA4C;IACjDC,IAAI,EAAE,6BAD2C;IAEjD0G;EAFiD,CAA5C,CAAP;AAID;;AACM,SAASmE,iBAAT,CAA2BtK,KAA3B,EAA+D;EACpE,OAAO,IAAAR,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCO;EAFuC,CAAlC,CAAP;AAID;;AACM,SAASuK,oBAAT,GAAwD;EAC7D,OAAO;IACL9K,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS+K,sBAAT,CACLhB,cAA6D,GAAG,IAD3D,EAELtH,MAFK,EAGLuI,IAA4C,GAAG,IAH1C,EAILC,UAJK,EAKqB;EAC1B,OAAO,IAAAlL,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5C+J,cAF4C;IAG5CtH,MAH4C;IAI5CuI,IAJ4C;IAK5CC;EAL4C,CAAvC,CAAP;AAOD;;AACM,SAASC,iBAAT,CACLpI,IAAqC,GAAG,IADnC,EAELoG,cAFK,EAGgB;EACrB,OAAO,IAAAnJ,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC8C,IAFuC;IAGvCoG;EAHuC,CAAlC,CAAP;AAKD;;AACM,SAASiC,qBAAT,CACL3I,EADK,EAELuH,cAAmD,GAAG,IAFjD,EAGoB;EACzB,OAAO,IAAAhK,qBAAA,EAAsC;IAC3CC,IAAI,EAAE,uBADqC;IAE3CwC,EAF2C;IAG3CuH;EAH2C,CAAtC,CAAP;AAKD;;AACM,SAASqB,iBAAT,GAAkD;EACvD,OAAO;IACLpL,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASqL,gBAAT,CACL7I,EADK,EAELuH,cAAmD,GAAG,IAFjD,EAGe;EACpB,OAAO,IAAAhK,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtCuH;EAHsC,CAAjC,CAAP;AAKD;;AACM,SAASuB,oBAAT,CACL9I,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGLE,QAAsD,GAAG,IAHpD,EAILtJ,IAJK,EAKmB;EACxB,OAAO,IAAAZ,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CwC,EAF0C;IAG1CuH,cAH0C;IAI1CG,OAAO,EAAED,QAJiC;IAK1CtJ;EAL0C,CAArC,CAAP;AAOD;;AACM,SAAS4K,uBAAT,CACLtB,QAAsD,GAAG,IADpD,EAELtJ,IAFK,EAGsB;EAC3B,OAAO,IAAAZ,qBAAA,EAAwC;IAC7CC,IAAI,EAAE,yBADuC;IAE7CkK,OAAO,EAAED,QAFoC;IAG7CtJ;EAH6C,CAAxC,CAAP;AAKD;;AACM,SAAS6K,0BAAT,CACLC,KADK,EAEyB;EAC9B,OAAO,IAAA1L,qBAAA,EAA2C;IAChDC,IAAI,EAAE,4BAD0C;IAEhDyL;EAFgD,CAA3C,CAAP;AAID;;AACM,SAASC,mBAAT,GAAsD;EAC3D,OAAO;IACL1L,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS2L,mBAAT,GAAsD;EAC3D,OAAO;IACL3L,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS4L,sBAAT,CACL1C,cADK,EAEqB;EAC1B,OAAO,IAAAnJ,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CkJ;EAF4C,CAAvC,CAAP;AAID;;AACM,SAAS2C,2BAAT,CACLtL,KADK,EAE0B;EAC/B,OAAO,IAAAR,qBAAA,EAA4C;IACjDC,IAAI,EAAE,6BAD2C;IAEjDO;EAFiD,CAA5C,CAAP;AAID;;AACM,SAASuL,oBAAT,GAAwD;EAC7D,OAAO;IACL9L,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS+L,oBAAT,CACL5H,UADK,EAEL6H,QAAoC,GAAG,EAFlC,EAGLC,cAA+C,GAAG,EAH7C,EAILC,aAA8C,GAAG,EAJ5C,EAKLC,KAAc,GAAG,KALZ,EAMmB;EACxB,OAAO,IAAApM,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CmE,UAF0C;IAG1C6H,QAH0C;IAI1CC,cAJ0C;IAK1CC,aAL0C;IAM1CC;EAN0C,CAArC,CAAP;AAQD;;AACM,SAASC,sBAAT,CACL5J,EADK,EAELjC,KAFK,EAGLsD,QAHK,EAILgE,OAJK,EAKLwE,MALK,EAMqB;EAC1B,OAAO,IAAAtM,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CwC,EAF4C;IAG5CjC,KAH4C;IAI5CsD,QAJ4C;IAK5CiE,MAAM,EAAED,OALoC;IAM5CwE;EAN4C,CAAvC,CAAP;AAQD;;AACM,SAASC,sBAAT,CACL/L,KADK,EAEqB;EAC1B,OAAO,IAAAR,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CO,KAF4C;IAG5CuH,MAAM,EAAE;EAHoC,CAAvC,CAAP;AAKD;;AACM,SAASyE,iBAAT,CACL/J,EAAmC,GAAG,IADjC,EAEL8B,GAFK,EAGL/D,KAHK,EAILiM,QAA2B,GAAG,IAJzB,EAKgB;EACrB,OAAO,IAAAzM,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCwC,EAFuC;IAGvC8B,GAHuC;IAIvC/D,KAJuC;IAKvCiM,QALuC;IAMvC1E,MAAM,EAAE;EAN+B,CAAlC,CAAP;AAQD;;AACM,SAAS2E,kBAAT,CACLnI,GADK,EAEL/D,KAFK,EAGLiM,QAA2B,GAAG,IAHzB,EAIiB;EACtB,OAAO,IAAAzM,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCsE,GAFwC;IAGxC/D,KAHwC;IAIxCiM,QAJwC;IAKxCnI,IAAI,EAAE,IALkC;IAMxCgI,MAAM,EAAE,IANgC;IAOxCxI,QAAQ,EAAE,IAP8B;IAQxC6I,KAAK,EAAE,IARiC;IASxC5E,MAAM,EAAE;EATgC,CAAnC,CAAP;AAWD;;AACM,SAAS6E,wBAAT,CACLhI,QADK,EAEuB;EAC5B,OAAO,IAAA5E,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9C2E;EAF8C,CAAzC,CAAP;AAID;;AACM,SAASiI,UAAT,CACLpK,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGLU,SAAwC,GAAG,IAHtC,EAILoC,QAJK,EAKS;EACd,OAAO,IAAA9M,qBAAA,EAA2B;IAChCC,IAAI,EAAE,YAD0B;IAEhCwC,EAFgC;IAGhCuH,cAHgC;IAIhCU,SAJgC;IAKhCoC;EALgC,CAA3B,CAAP;AAOD;;AACM,SAASC,uBAAT,CACLtK,EADK,EAELuK,aAFK,EAGsB;EAC3B,OAAO,IAAAhN,qBAAA,EAAwC;IAC7CC,IAAI,EAAE,yBADuC;IAE7CwC,EAF6C;IAG7CuK;EAH6C,CAAxC,CAAP;AAKD;;AACM,SAASC,2BAAT,CACLzM,KADK,EAE0B;EAC/B,OAAO,IAAAR,qBAAA,EAA4C;IACjDC,IAAI,EAAE,6BAD2C;IAEjDO;EAFiD,CAA5C,CAAP;AAID;;AACM,SAAS0M,oBAAT,GAAwD;EAC7D,OAAO;IACLjN,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASkN,oBAAT,GAAwD;EAC7D,OAAO;IACLlN,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASmN,kBAAT,GAAoD;EACzD,OAAO;IACLnN,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASoN,mBAAT,CACL3B,KADK,EAEkB;EACvB,OAAO,IAAA1L,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCyL;EAFyC,CAApC,CAAP;AAID;;AACM,SAAS4B,oBAAT,CACL1I,QADK,EAEmB;EACxB,OAAO,IAAA5E,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1C2E;EAF0C,CAArC,CAAP;AAID;;AACM,SAAS2I,SAAT,CACL9K,EADK,EAELuH,cAA6D,GAAG,IAF3D,EAGL3J,KAHK,EAIQ;EACb,OAAO,IAAAL,qBAAA,EAA0B;IAC/BC,IAAI,EAAE,WADyB;IAE/BwC,EAF+B;IAG/BuH,cAH+B;IAI/B3J;EAJ+B,CAA1B,CAAP;AAMD;;AACM,SAAS8I,cAAT,CAAwBA,cAAxB,EAAsE;EAC3E,OAAO,IAAAnJ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkJ;EAFoC,CAA/B,CAAP;AAID;;AACM,SAASqE,kBAAT,CACLzL,UADK,EAELoH,cAFK,EAGiB;EACtB,OAAO,IAAAnJ,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExC8B,UAFwC;IAGxCoH;EAHwC,CAAnC,CAAP;AAKD;;AACM,SAASsE,aAAT,CACLC,KAA8B,GAAG,IAD5B,EAELC,QAA2B,GAAG,IAFzB,EAGLlB,QAA2B,GAAG,IAHzB,EAIY;EACjB,OAAO,IAAAzM,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCyN,KAFmC;IAGnCE,OAAO,EAAED,QAH0B;IAInClB,QAJmC;IAKnC1J,IAAI,EAAE;EAL6B,CAA9B,CAAP;AAOD;;AACM,SAAS8K,wBAAT,CACLnL,MADK,EAEuB;EAC5B,OAAO,IAAA1C,qBAAA,EAAyC;IAC9CC,IAAI,EAAE,0BADwC;IAE9CyC;EAF8C,CAAzC,CAAP;AAID;;AACM,SAASoL,0BAAT,CACLpL,MADK,EAEyB;EAC9B,OAAO,IAAA1C,qBAAA,EAA2C;IAChDC,IAAI,EAAE,4BAD0C;IAEhDyC;EAFgD,CAA3C,CAAP;AAID;;AACM,SAASqL,mBAAT,CACLrC,KADK,EAEkB;EACvB,OAAO,IAAA1L,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCyL;EAFyC,CAApC,CAAP;AAID;;AACM,SAASe,QAAT,CAAkBnI,IAAlB,EAAsD;EAC3D,OAAO,IAAAtE,qBAAA,EAAyB;IAC9BC,IAAI,EAAE,UADwB;IAE9BqE;EAF8B,CAAzB,CAAP;AAID;;AACM,SAAS0J,kBAAT,GAAoD;EACzD,OAAO;IACL/N,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASgO,eAAT,CACLxL,EADK,EAEL7B,IAFK,EAOc;EACnB,OAAO,IAAAZ,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwC,EAFqC;IAGrC7B;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASsN,eAAT,CACLC,OADK,EAEc;EACnB,OAAO,IAAAnO,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCkO,OAFqC;IAGrCC,YAAY,EAAE,IAHuB;IAIrCC,iBAAiB,EAAE;EAJkB,CAAhC,CAAP;AAMD;;AACM,SAASC,cAAT,CACLH,OADK,EAEa;EAClB,OAAO,IAAAnO,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkO,OAFoC;IAGpCC,YAAY,EAAE,IAHsB;IAIpCC,iBAAiB,EAAE;EAJiB,CAA/B,CAAP;AAMD;;AACM,SAASE,cAAT,CACLJ,OADK,EAEa;EAClB,OAAO,IAAAnO,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkO,OAFoC;IAGpCC,YAAY,EAAE,IAHsB;IAIpCC,iBAAiB,EAAE;EAJiB,CAA/B,CAAP;AAMD;;AACM,SAASG,cAAT,CACLL,OADK,EAEa;EAClB,OAAO,IAAAnO,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkO,OAFoC;IAGpCE,iBAAiB,EAAE;EAHiB,CAA/B,CAAP;AAKD;;AACM,SAASI,iBAAT,CAA2BhM,EAA3B,EAAkE;EACvE,OAAO,IAAAzC,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCwC,EAFuC;IAGvCH,IAAI,EAAE;EAHiC,CAAlC,CAAP;AAKD;;AACM,SAASoM,gBAAT,CACLjM,EADK,EAELH,IAFK,EAGe;EACpB,OAAO,IAAAtC,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtCH;EAHsC,CAAjC,CAAP;AAKD;;AACM,SAASqM,gBAAT,CACLlM,EADK,EAELH,IAFK,EAGe;EACpB,OAAO,IAAAtC,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCwC,EAFsC;IAGtCH;EAHsC,CAAjC,CAAP;AAKD;;AACM,SAASsM,mBAAT,CAA6BnM,EAA7B,EAAsE;EAC3E,OAAO,IAAAzC,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCwC;EAFyC,CAApC,CAAP;AAID;;AACM,SAASoM,iBAAT,CACLC,UADK,EAELC,SAFK,EAGgB;EACrB,OAAO,IAAA/O,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC6O,UAFuC;IAGvCC;EAHuC,CAAlC,CAAP;AAKD;;AACM,SAASC,yBAAT,CACLF,UADK,EAELC,SAFK,EAGwB;EAC7B,OAAO,IAAA/O,qBAAA,EAA0C;IAC/CC,IAAI,EAAE,2BADyC;IAE/C6O,UAF+C;IAG/CC,SAH+C;IAI/CjL,QAAQ,EAAE;EAJqC,CAA1C,CAAP;AAMD;;AACM,SAASmL,YAAT,CACLlM,IADK,EAELvC,KAKQ,GAAG,IAPN,EAQW;EAChB,OAAO,IAAAR,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElC8C,IAFkC;IAGlCvC;EAHkC,CAA7B,CAAP;AAKD;;AAEM,SAAS0O,iBAAT,CACLnM,IADK,EAEgB;EACrB,OAAO,IAAA/C,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC8C;EAFuC,CAAlC,CAAP;AAID;;AAEM,SAASoM,UAAT,CACLC,cADK,EAELC,cAAsD,GAAG,IAFpD,EAGLC,QAHK,EAULC,WAA2B,GAAG,IAVzB,EAWS;EACd,OAAO,IAAAvP,qBAAA,EAA2B;IAChCC,IAAI,EAAE,YAD0B;IAEhCmP,cAFgC;IAGhCC,cAHgC;IAIhCC,QAJgC;IAKhCC;EALgC,CAA3B,CAAP;AAOD;;AAEM,SAASC,kBAAT,GAAoD;EACzD,OAAO;IACLvP,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASwP,sBAAT,CACL1N,UADK,EAEqB;EAC1B,OAAO,IAAA/B,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5C8B;EAF4C,CAAvC,CAAP;AAID;;AAEM,SAAS2N,cAAT,CAAwB3N,UAAxB,EAAoE;EACzE,OAAO,IAAA/B,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpC8B;EAFoC,CAA/B,CAAP;AAID;;AAEM,SAAS4N,aAAT,CAAuB5M,IAAvB,EAAsD;EAC3D,OAAO,IAAA/C,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnC8C;EAFmC,CAA9B,CAAP;AAID;;AAEM,SAAS6M,mBAAT,CACLjM,MADK,EAELC,QAFK,EAGkB;EACvB,OAAO,IAAA5D,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC0D,MAFyC;IAGzCC;EAHyC,CAApC,CAAP;AAKD;;AAEM,SAASiM,iBAAT,CACLC,SADK,EAEL/M,IAFK,EAGgB;EACrB,OAAO,IAAA/C,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC6P,SAFuC;IAGvC/M;EAHuC,CAAlC,CAAP;AAKD;;AAEM,SAASgN,iBAAT,CACLhN,IADK,EAELiN,UAFK,EAGLT,WAAoB,GAAG,KAHlB,EAIgB;EACrB,OAAO,IAAAvP,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC8C,IAFuC;IAGvCiN,UAHuC;IAIvCT;EAJuC,CAAlC,CAAP;AAMD;;AAEM,SAASU,kBAAT,CACLrL,QADK,EAEiB;EACtB,OAAO,IAAA5E,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExC2E;EAFwC,CAAnC,CAAP;AAID;;AAEM,SAASsL,OAAT,CAAiB1P,KAAjB,EAA2C;EAChD,OAAO,IAAAR,qBAAA,EAAwB;IAC7BC,IAAI,EAAE,SADuB;IAE7BO;EAF6B,CAAxB,CAAP;AAID;;AAEM,SAAS2P,WAAT,CACLC,eADK,EAELC,eAFK,EAGLf,QAHK,EAUU;EACf,OAAO,IAAAtP,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCmQ,eAFiC;IAGjCC,eAHiC;IAIjCf;EAJiC,CAA5B,CAAP;AAMD;;AAEM,SAASgB,kBAAT,GAAoD;EACzD,OAAO;IACLrQ,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASsQ,kBAAT,GAAoD;EACzD,OAAO;IACLtQ,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASuQ,IAAT,GAAwB;EAC7B,OAAO;IACLvQ,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASwQ,WAAT,CACLC,YADK,EAUL3N,IAVK,EAWU;EACf,OAAO,IAAA/C,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCyQ,YAFiC;IAGjC3N;EAHiC,CAA5B,CAAP;AAKD;;AACM,SAAS4N,qBAAT,CAA+B5N,IAA/B,EAAsE;EAC3E,OAAO,IAAA/C,qBAAA,EAAsC;IAC3CC,IAAI,EAAE,uBADqC;IAE3C8C;EAF2C,CAAtC,CAAP;AAID;;AACM,SAAS6N,mBAAT,GAAsD;EAC3D,OAAO;IACL3Q,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAAS4Q,cAAT,CACLlN,MADK,EAEL1C,MAFK,EAGa;EAClB,OAAO,IAAAjB,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpC0D,MAFoC;IAGpC1C;EAHoC,CAA/B,CAAP;AAKD;;AACM,SAAS6P,eAAT,CACLvM,GADK,EAEL/D,KAFK,EAGc;EACnB,OAAO,IAAAR,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCsE,GAFqC;IAGrC/D;EAHqC,CAAhC,CAAP;AAKD;;AACM,SAASuQ,SAAT,CAAmBhP,UAAnB,EAA0D;EAC/D,OAAO,IAAA/B,qBAAA,EAA0B;IAC/BC,IAAI,EAAE,WADyB;IAE/B8B;EAF+B,CAA1B,CAAP;AAID;;AACM,SAASiP,YAAT,CACLpQ,IADK,EAELgC,KAAc,GAAG,KAFZ,EAGW;EAChB,OAAO,IAAA5C,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCW,IAFkC;IAGlCgC;EAHkC,CAA7B,CAAP;AAKD;;AACM,SAASqO,sBAAT,CACL/J,QADK,EAEqB;EAC1B,OAAO,IAAAlH,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CiH;EAF4C,CAAvC,CAAP;AAID;;AACM,SAASgK,gBAAT,CACL9M,UADK,EAEe;EACpB,OAAO,IAAApE,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCmE;EAFsC,CAAjC,CAAP;AAID;;AACM,SAAS+M,eAAT,CACLpR,QAA+C,GAAG,EAD7C,EAEc;EACnB,OAAO,IAAAC,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCF;EAFqC,CAAhC,CAAP;AAID;;AACM,SAASqR,cAAT,CAAwB5Q,KAAxB,EAAyD;EAC9D,OAAO,IAAAR,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCO;EAFoC,CAA/B,CAAP;AAID;;AACM,SAAS6Q,gBAAT,CAA0BzQ,IAA1B,EAA+D;EACpE,OAAO,IAAAZ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCW;EAFsC,CAAjC,CAAP;AAID;;AACM,SAAS0Q,cAAT,GAA4C;EACjD,OAAO;IACLrR,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASsR,uBAAT,CACLxP,UADK,EAEsB;EAC3B,OAAO,IAAA/B,qBAAA,EAAwC;IAC7CC,IAAI,EAAE,yBADuC;IAE7C8B;EAF6C,CAAxC,CAAP;AAID;;AACM,SAASyP,oBAAT,CACLvQ,MADK,EAEmB;EACxB,OAAO,IAAAjB,qBAAA,EAAqC;IAC1CC,IAAI,EAAE,sBADoC;IAE1CgB;EAF0C,CAArC,CAAP;AAID;;AACM,SAASwQ,6BAAT,GAA0E;EAC/E,OAAO;IACLxR,IAAI,EAAE;EADD,CAAP;AAGD;;AACM,SAASyR,mBAAT,CACLC,SADK,EAEkB;EACvB,OAAO,IAAA3R,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC0R;EAFyC,CAApC,CAAP;AAID;;AAEM,SAASC,iBAAT,CACLnP,EAAmC,GAAG,IADjC,EAELuH,cAIa,GAAG,IANX,EAOLtH,MAPK,EAQLwI,UAA8C,GAAG,IAR5C,EASgB;EACrB,OAAO,IAAAlL,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCwC,EAFuC;IAGvCuH,cAHuC;IAIvCtH,MAJuC;IAKvCwI;EALuC,CAAlC,CAAP;AAOD;;AAEM,SAAS2G,eAAT,CACLnN,UAAiD,GAAG,IAD/C,EAELH,GAFK,EAQLyF,cAIa,GAAG,IAZX,EAaLtH,MAbK,EAgBLwI,UAA8C,GAAG,IAhB5C,EAiBc;EACnB,OAAO,IAAAlL,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCyE,UAFqC;IAGrCH,GAHqC;IAIrCyF,cAJqC;IAKrCtH,MALqC;IAMrCwI;EANqC,CAAhC,CAAP;AAQD;;AAEM,SAAS4G,eAAT,CACL1R,IADK,EAELC,KAFK,EAGc;EACnB,OAAO,IAAAL,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCG,IAFqC;IAGrCC;EAHqC,CAAhC,CAAP;AAKD;;AAEM,SAAS0R,0BAAT,CACL/H,cAA+D,GAAG,IAD7D,EAELgI,UAFK,EAGL7I,cAAyC,GAAG,IAHvC,EAIyB;EAC9B,OAAO,IAAAnJ,qBAAA,EAA2C;IAChDC,IAAI,EAAE,4BAD0C;IAEhD+J,cAFgD;IAGhDgI,UAHgD;IAIhD7I;EAJgD,CAA3C,CAAP;AAMD;;AAEM,SAAS8I,+BAAT,CACLjI,cAA+D,GAAG,IAD7D,EAELgI,UAFK,EAGL7I,cAAyC,GAAG,IAHvC,EAI8B;EACnC,OAAO,IAAAnJ,qBAAA,EAAgD;IACrDC,IAAI,EAAE,iCAD+C;IAErD+J,cAFqD;IAGrDgI,UAHqD;IAIrD7I;EAJqD,CAAhD,CAAP;AAMD;;AAEM,SAAS+I,mBAAT,CACL3N,GADK,EAEL4E,cAAyC,GAAG,IAFvC,EAGLgJ,WAAgC,GAAG,IAH9B,EAIkB;EACvB,OAAO,IAAAnS,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCsE,GAFyC;IAGzC4E,cAHyC;IAIzCgJ,WAJyC;IAKzC7N,IAAI,EAAE;EALmC,CAApC,CAAP;AAOD;;AAEM,SAAS8N,iBAAT,CACL7N,GADK,EAELyF,cAA+D,GAAG,IAF7D,EAGLgI,UAHK,EAIL7I,cAAyC,GAAG,IAJvC,EAKgB;EACrB,OAAO,IAAAnJ,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCsE,GAFuC;IAGvCyF,cAHuC;IAIvCgI,UAJuC;IAKvC7I,cALuC;IAMvC7E,IAAI,EAAE;EANiC,CAAlC,CAAP;AAQD;;AAEM,SAAS+N,gBAAT,CACLL,UADK,EAEL7I,cAAyC,GAAG,IAFvC,EAGe;EACpB,OAAO,IAAAnJ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtC+R,UAFsC;IAGtC7I;EAHsC,CAAjC,CAAP;AAKD;;AAEM,SAASmJ,YAAT,GAAwC;EAC7C,OAAO;IACLrS,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASsS,gBAAT,GAAgD;EACrD,OAAO;IACLtS,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASuS,eAAT,GAA8C;EACnD,OAAO;IACLvS,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASwS,kBAAT,GAAoD;EACzD,OAAO;IACLxS,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASyS,cAAT,GAA4C;EACjD,OAAO;IACLzS,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS0S,aAAT,GAA0C;EAC/C,OAAO;IACL1S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS2S,eAAT,GAA8C;EACnD,OAAO;IACL3S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS4S,eAAT,GAA8C;EACnD,OAAO;IACL5S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS6S,eAAT,GAA8C;EACnD,OAAO;IACL7S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS8S,eAAT,GAA8C;EACnD,OAAO;IACL9S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAAS+S,kBAAT,GAAoD;EACzD,OAAO;IACL/S,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASgT,gBAAT,GAAgD;EACrD,OAAO;IACLhT,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASiT,aAAT,GAA0C;EAC/C,OAAO;IACLjT,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASkT,UAAT,GAAoC;EACzC,OAAO;IACLlT,IAAI,EAAE;EADD,CAAP;AAGD;;AAEM,SAASmT,cAAT,CACLpJ,cAA+D,GAAG,IAD7D,EAELgI,UAFK,EAGL7I,cAAyC,GAAG,IAHvC,EAIa;EAClB,OAAO,IAAAnJ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpC+J,cAFoC;IAGpCgI,UAHoC;IAIpC7I;EAJoC,CAA/B,CAAP;AAMD;;AAEM,SAASkK,iBAAT,CACLrJ,cAA+D,GAAG,IAD7D,EAELgI,UAFK,EAGL7I,cAAyC,GAAG,IAHvC,EAIgB;EACrB,OAAO,IAAAnJ,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvC+J,cAFuC;IAGvCgI,UAHuC;IAIvC7I;EAJuC,CAAlC,CAAP;AAMD;;AAEM,SAASmK,eAAT,CACLC,QADK,EAELvJ,cAAqD,GAAG,IAFnD,EAGc;EACnB,OAAO,IAAAhK,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCsT,QAFqC;IAGrCvJ;EAHqC,CAAhC,CAAP;AAKD;;AAEM,SAASwJ,eAAT,CACLC,aADK,EAELtK,cAAyC,GAAG,IAFvC,EAGLuK,OAAuB,GAAG,IAHrB,EAIc;EACnB,OAAO,IAAA1T,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCwT,aAFqC;IAGrCtK,cAHqC;IAIrCuK;EAJqC,CAAhC,CAAP;AAMD;;AAEM,SAASC,WAAT,CACLC,QADK,EAEL5J,cAAqD,GAAG,IAFnD,EAGU;EACf,OAAO,IAAAhK,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjC2T,QAFiC;IAGjC5J;EAHiC,CAA5B,CAAP;AAKD;;AAEM,SAAS6J,aAAT,CACL1F,OADK,EAEY;EACjB,OAAO,IAAAnO,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCkO;EAFmC,CAA9B,CAAP;AAID;;AAEM,SAAS2F,WAAT,CAAqBnK,WAArB,EAA2D;EAChE,OAAO,IAAA3J,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjC0J;EAFiC,CAA5B,CAAP;AAID;;AAEM,SAASoK,WAAT,CACLC,YADK,EAEU;EACf,OAAO,IAAAhU,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjC+T;EAFiC,CAA5B,CAAP;AAID;;AAEM,SAASC,cAAT,CAAwB9K,cAAxB,EAAoE;EACzE,OAAO,IAAAnJ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkJ;EAFoC,CAA/B,CAAP;AAID;;AAEM,SAAS+K,UAAT,CAAoB/K,cAApB,EAA4D;EACjE,OAAO,IAAAnJ,qBAAA,EAA2B;IAChCC,IAAI,EAAE,YAD0B;IAEhCkJ;EAFgC,CAA3B,CAAP;AAID;;AAEM,SAASgL,kBAAT,CACLpT,KADK,EAEL4I,WAFK,EAGL7F,QAAiB,GAAG,KAHf,EAIiB;EACtB,OAAO,IAAA9D,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCc,KAFwC;IAGxC4I,WAHwC;IAIxC7F;EAJwC,CAAnC,CAAP;AAMD;;AAEM,SAASsQ,WAAT,CAAqB1I,KAArB,EAA4D;EACjE,OAAO,IAAA1L,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCyL;EAFiC,CAA5B,CAAP;AAID;;AAEM,SAAS2I,kBAAT,CACL3I,KADK,EAEiB;EACtB,OAAO,IAAA1L,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExCyL;EAFwC,CAAnC,CAAP;AAID;;AAEM,SAAS4I,iBAAT,CACLC,SADK,EAELC,WAFK,EAGLC,QAHK,EAILC,SAJK,EAKgB;EACrB,OAAO,IAAA1U,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCsU,SAFuC;IAGvCC,WAHuC;IAIvCC,QAJuC;IAKvCC;EALuC,CAAlC,CAAP;AAOD;;AAEM,SAASC,WAAT,CAAqBlH,aAArB,EAAsE;EAC3E,OAAO,IAAAzN,qBAAA,EAA4B;IACjCC,IAAI,EAAE,aAD2B;IAEjCwN;EAFiC,CAA5B,CAAP;AAID;;AAEM,SAASmH,mBAAT,CACLzL,cADK,EAEkB;EACvB,OAAO,IAAAnJ,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCkJ;EAFyC,CAApC,CAAP;AAID;;AAEM,SAAS0L,cAAT,CAAwB1L,cAAxB,EAAoE;EACzE,OAAO,IAAAnJ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpCkJ,cAFoC;IAGpChJ,QAAQ,EAAE;EAH0B,CAA/B,CAAP;AAKD;;AAEM,SAAS2U,mBAAT,CACLhG,UADK,EAELC,SAFK,EAGkB;EACvB,OAAO,IAAA/O,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC6O,UAFyC;IAGzCC;EAHyC,CAApC,CAAP;AAKD;;AAEM,SAASgG,YAAT,CACLtH,aADK,EAELtE,cAA+B,GAAG,IAF7B,EAGL6L,QAAyB,GAAG,IAHvB,EAIW;EAChB,OAAO,IAAAhV,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCwN,aAFkC;IAGlCtE,cAHkC;IAIlC6L;EAJkC,CAA7B,CAAP;AAMD;;AAEM,SAASC,aAAT,CACLC,OADK,EAQY;EACjB,OAAO,IAAAlV,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCiV;EAFmC,CAA9B,CAAP;AAID;;AAEM,SAASC,6BAAT,CACLpT,UADK,EAELiI,cAAqD,GAAG,IAFnD,EAG4B;EACjC,OAAO,IAAAhK,qBAAA,EAA8C;IACnDC,IAAI,EAAE,+BAD6C;IAEnD8B,UAFmD;IAGnDiI;EAHmD,CAA9C,CAAP;AAKD;;AAEM,SAASoL,sBAAT,CACL3S,EADK,EAELuH,cAA+D,GAAG,IAF7D,EAGLE,QAAmE,GAAG,IAHjE,EAILtJ,IAJK,EAKqB;EAC1B,OAAO,IAAAZ,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CwC,EAF4C;IAG5CuH,cAH4C;IAI5CG,OAAO,EAAED,QAJmC;IAK5CtJ;EAL4C,CAAvC,CAAP;AAOD;;AAEM,SAASyU,eAAT,CACLzU,IADK,EAEc;EACnB,OAAO,IAAAZ,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCW;EAFqC,CAAhC,CAAP;AAID;;AAEM,SAAS0U,sBAAT,CACL7S,EADK,EAELuH,cAA+D,GAAG,IAF7D,EAGLb,cAHK,EAIqB;EAC1B,OAAO,IAAAnJ,qBAAA,EAAuC;IAC5CC,IAAI,EAAE,wBADsC;IAE5CwC,EAF4C;IAG5CuH,cAH4C;IAI5Cb;EAJ4C,CAAvC,CAAP;AAMD;;AAEM,SAASoM,yBAAT,CACLxT,UADK,EAELiI,cAAqD,GAAG,IAFnD,EAGwB;EAC7B,OAAO,IAAAhK,qBAAA,EAA0C;IAC/CC,IAAI,EAAE,2BADyC;IAE/C8B,UAF+C;IAG/CiI;EAH+C,CAA1C,CAAP;AAKD;;AAEM,SAASwL,cAAT,CACLzT,UADK,EAELoH,cAFK,EAGa;EAClB,OAAO,IAAAnJ,qBAAA,EAA+B;IACpCC,IAAI,EAAE,gBAD8B;IAEpC8B,UAFoC;IAGpCoH;EAHoC,CAA/B,CAAP;AAKD;;AAEM,SAASsM,eAAT,CACLtM,cADK,EAELpH,UAFK,EAGc;EACnB,OAAO,IAAA/B,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErCkJ,cAFqC;IAGrCpH;EAHqC,CAAhC,CAAP;AAKD;;AAEM,SAAS2T,iBAAT,CACLjT,EADK,EAEL0L,OAFK,EAGgB;EACrB,OAAO,IAAAnO,qBAAA,EAAkC;IACvCC,IAAI,EAAE,mBADiC;IAEvCwC,EAFuC;IAGvC0L;EAHuC,CAAlC,CAAP;AAKD;;AAEM,SAASwH,YAAT,CACLlT,EADK,EAEL0P,WAAgC,GAAG,IAF9B,EAGW;EAChB,OAAO,IAAAnS,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElCwC,EAFkC;IAGlC0P;EAHkC,CAA7B,CAAP;AAKD;;AAEM,SAASyD,mBAAT,CACLnT,EADK,EAEL7B,IAFK,EAGkB;EACvB,OAAO,IAAAZ,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzCwC,EAFyC;IAGzC7B;EAHyC,CAApC,CAAP;AAKD;;AAEM,SAASiV,aAAT,CAAuBjV,IAAvB,EAAkE;EACvE,OAAO,IAAAZ,qBAAA,EAA8B;IACnCC,IAAI,EAAE,eAD6B;IAEnCW;EAFmC,CAA9B,CAAP;AAID;;AAEM,SAASkV,YAAT,CACLlR,QADK,EAELmR,SAAgC,GAAG,IAF9B,EAGL/L,cAAqD,GAAG,IAHnD,EAIW;EAChB,OAAO,IAAAhK,qBAAA,EAA6B;IAClCC,IAAI,EAAE,cAD4B;IAElC2E,QAFkC;IAGlCmR,SAHkC;IAIlC/L;EAJkC,CAA7B,CAAP;AAMD;;AAEM,SAASgM,yBAAT,CACLvT,EADK,EAELwT,eAFK,EAGwB;EAC7B,OAAO,IAAAjW,qBAAA,EAA0C;IAC/CC,IAAI,EAAE,2BADyC;IAE/CwC,EAF+C;IAG/CwT,eAH+C;IAI/CC,QAAQ,EAAE;EAJqC,CAA1C,CAAP;AAMD;;AAEM,SAASC,yBAAT,CACLpU,UADK,EAEwB;EAC7B,OAAO,IAAA/B,qBAAA,EAA0C;IAC/CC,IAAI,EAAE,2BADyC;IAE/C8B;EAF+C,CAA1C,CAAP;AAID;;AAEM,SAASqU,mBAAT,CACLrU,UADK,EAEkB;EACvB,OAAO,IAAA/B,qBAAA,EAAoC;IACzCC,IAAI,EAAE,qBADmC;IAEzC8B;EAFyC,CAApC,CAAP;AAID;;AAEM,SAASsU,kBAAT,CACLtU,UADK,EAEiB;EACtB,OAAO,IAAA/B,qBAAA,EAAmC;IACxCC,IAAI,EAAE,oBADkC;IAExC8B;EAFwC,CAAnC,CAAP;AAID;;AAEM,SAASuU,4BAAT,CACL7T,EADK,EAE2B;EAChC,OAAO,IAAAzC,qBAAA,EAA6C;IAClDC,IAAI,EAAE,8BAD4C;IAElDwC;EAFkD,CAA7C,CAAP;AAID;;AAEM,SAAS8T,gBAAT,CAA0BpN,cAA1B,EAAwE;EAC7E,OAAO,IAAAnJ,qBAAA,EAAiC;IACtCC,IAAI,EAAE,kBADgC;IAEtCkJ;EAFsC,CAAjC,CAAP;AAID;;AAEM,SAASqN,4BAAT,CACL9T,MADK,EAE2B;EAChC,OAAO,IAAA1C,qBAAA,EAA6C;IAClDC,IAAI,EAAE,8BAD4C;IAElDyC;EAFkD,CAA7C,CAAP;AAID;;AAEM,SAAS+T,0BAAT,CACL/T,MADK,EAEyB;EAC9B,OAAO,IAAA1C,qBAAA,EAA2C;IAChDC,IAAI,EAAE,4BAD0C;IAEhDyC;EAFgD,CAA3C,CAAP;AAID;;AAEM,SAASgU,eAAT,CACLC,UAAuC,GAAG,IADrC,EAELhJ,QAAqC,GAAG,IAFnC,EAGL5K,IAHK,EAIc;EACnB,OAAO,IAAA/C,qBAAA,EAAgC;IACrCC,IAAI,EAAE,iBAD+B;IAErC0W,UAFqC;IAGrC/I,OAAO,EAAED,QAH4B;IAIrC5K;EAJqC,CAAhC,CAAP;AAMD;;AAGD,SAAS6T,aAAT,CAAuBpW,KAAvB,EAAsC;EACpCqW,OAAO,CAACC,KAAR,CACE,gEADF;EAGA,OAAO3T,cAAc,CAAC3C,KAAD,CAArB;AACD;;AAGD,SAASuW,YAAT,CAAsBxT,OAAtB,EAAuCC,KAAa,GAAG,EAAvD,EAA2D;EACzDqT,OAAO,CAACC,KAAR,CAAc,8DAAd;EACA,OAAOxT,aAAa,CAACC,OAAD,EAAUC,KAAV,CAApB;AACD;;AAGD,SAASwT,YAAT,CAAsBpS,QAAtB,EAAwC;EACtCiS,OAAO,CAACC,KAAR,CAAc,4DAAd;EACA,OAAOnS,WAAW,CAACC,QAAD,CAAlB;AACD;;AAGD,SAASqS,cAAT,CAAwBrS,QAAxB,EAAgD;EAC9CiS,OAAO,CAACC,KAAR,CACE,gEADF;EAGA,OAAO7O,aAAa,CAACrD,QAAD,CAApB;AACD"}