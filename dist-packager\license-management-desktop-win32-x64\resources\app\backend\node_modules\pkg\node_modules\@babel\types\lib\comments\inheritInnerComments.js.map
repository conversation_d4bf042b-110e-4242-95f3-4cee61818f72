{"version": 3, "names": ["inheritInnerComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritInnerComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit\";\nimport type * as t from \"..\";\n\nexport default function inheritInnerComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"innerComments\", child, parent);\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,oBAAT,CACbC,KADa,EAEbC,MAFa,EAGP;EACN,IAAAC,gBAAA,EAAQ,eAAR,EAAyBF,KAAzB,EAAgCC,MAAhC;AACD"}