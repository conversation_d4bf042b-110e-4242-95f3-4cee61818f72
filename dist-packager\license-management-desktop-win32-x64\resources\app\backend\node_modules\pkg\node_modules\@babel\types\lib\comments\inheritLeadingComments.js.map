{"version": 3, "names": ["inheritLeadingComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritLeadingComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit\";\nimport type * as t from \"..\";\n\nexport default function inheritLeadingComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"leadingComments\", child, parent);\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,sBAAT,CACbC,KADa,EAEbC,MAFa,EAGP;EACN,IAAAC,gBAAA,EAAQ,iBAAR,EAA2BF,KAA3B,EAAkCC,MAAlC;AACD"}