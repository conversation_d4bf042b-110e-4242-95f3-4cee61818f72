{"version": 3, "names": ["toBlock", "node", "parent", "isBlockStatement", "blockNodes", "isEmptyStatement", "isStatement", "isFunction", "returnStatement", "expressionStatement", "blockStatement"], "sources": ["../../src/converters/toBlock.ts"], "sourcesContent": ["import {\n  isBlockStatement,\n  isFunction,\n  isEmptyStatement,\n  isStatement,\n} from \"../validators/generated\";\nimport {\n  returnStatement,\n  expressionStatement,\n  blockStatement,\n} from \"../builders/generated\";\nimport type * as t from \"..\";\n\nexport default function toBlock(\n  node: t.Statement | t.Expression,\n  parent?: t.Node,\n): t.BlockStatement {\n  if (isBlockStatement(node)) {\n    return node;\n  }\n\n  let blockNodes: t.Statement[] = [];\n\n  if (isEmptyStatement(node)) {\n    blockNodes = [];\n  } else {\n    if (!isStatement(node)) {\n      if (isFunction(parent)) {\n        node = returnStatement(node);\n      } else {\n        node = expressionStatement(node);\n      }\n    }\n\n    blockNodes = [node];\n  }\n\n  return blockStatement(blockNodes);\n}\n"], "mappings": ";;;;;;;AAAA;;AAMA;;AAOe,SAASA,OAAT,CACbC,IADa,EAEbC,MAFa,EAGK;EAClB,IAAI,IAAAC,2BAAA,EAAiBF,IAAjB,CAAJ,EAA4B;IAC1B,OAAOA,IAAP;EACD;;EAED,IAAIG,UAAyB,GAAG,EAAhC;;EAEA,IAAI,IAAAC,2BAAA,EAAiBJ,IAAjB,CAAJ,EAA4B;IAC1BG,UAAU,GAAG,EAAb;EACD,CAFD,MAEO;IACL,IAAI,CAAC,IAAAE,sBAAA,EAAYL,IAAZ,CAAL,EAAwB;MACtB,IAAI,IAAAM,qBAAA,EAAWL,MAAX,CAAJ,EAAwB;QACtBD,IAAI,GAAG,IAAAO,2BAAA,EAAgBP,IAAhB,CAAP;MACD,CAFD,MAEO;QACLA,IAAI,GAAG,IAAAQ,+BAAA,EAAoBR,IAApB,CAAP;MACD;IACF;;IAEDG,UAAU,GAAG,CAACH,IAAD,CAAb;EACD;;EAED,OAAO,IAAAS,0BAAA,EAAeN,UAAf,CAAP;AACD"}