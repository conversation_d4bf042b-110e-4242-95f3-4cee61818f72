{"version": 3, "names": ["toExpression", "node", "isExpressionStatement", "expression", "isExpression", "isClass", "type", "isFunction", "Error"], "sources": ["../../src/converters/toExpression.ts"], "sourcesContent": ["import {\n  isExpression,\n  isFunction,\n  isClass,\n  isExpressionStatement,\n} from \"../validators/generated\";\nimport type * as t from \"..\";\n\nexport default toExpression as {\n  (node: t.Function): t.FunctionExpression;\n  (node: t.Class): t.ClassExpression;\n  (\n    node: t.ExpressionStatement | t.Expression | t.Class | t.Function,\n  ): t.Expression;\n};\n\nfunction toExpression(\n  node: t.ExpressionStatement | t.Expression | t.Class | t.Function,\n): t.Expression {\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  // return unmodified node\n  // important for things like ArrowFunctions where\n  // type change from ArrowFunction to FunctionExpression\n  // produces bugs like -> `()=>a` to `function () a`\n  // without generating a BlockStatement for it\n  // ref: https://github.com/babel/babili/issues/130\n  if (isExpression(node)) {\n    return node;\n  }\n\n  // convert all classes and functions\n  // ClassDeclaration -> ClassExpression\n  // FunctionDeclaration, ObjectMethod, ClassMethod -> FunctionExpression\n  if (isClass(node)) {\n    // @ts-expect-error todo(flow->ts): avoid type unsafe mutations\n    node.type = \"ClassExpression\";\n  } else if (isFunction(node)) {\n    // @ts-expect-error todo(flow->ts): avoid type unsafe mutations\n    node.type = \"FunctionExpression\";\n  }\n\n  // if it's still not an expression\n  if (!isExpression(node)) {\n    throw new Error(`cannot turn ${node.type} to an expression`);\n  }\n\n  return node;\n}\n"], "mappings": ";;;;;;;AAAA;;eAQeA,Y;;;AAQf,SAASA,YAAT,CACEC,IADF,EAEgB;EACd,IAAI,IAAAC,gCAAA,EAAsBD,IAAtB,CAAJ,EAAiC;IAC/BA,IAAI,GAAGA,IAAI,CAACE,UAAZ;EACD;;EAQD,IAAI,IAAAC,uBAAA,EAAaH,IAAb,CAAJ,EAAwB;IACtB,OAAOA,IAAP;EACD;;EAKD,IAAI,IAAAI,kBAAA,EAAQJ,IAAR,CAAJ,EAAmB;IAEjBA,IAAI,CAACK,IAAL,GAAY,iBAAZ;EACD,CAHD,MAGO,IAAI,IAAAC,qBAAA,EAAWN,IAAX,CAAJ,EAAsB;IAE3BA,IAAI,CAACK,IAAL,GAAY,oBAAZ;EACD;;EAGD,IAAI,CAAC,IAAAF,uBAAA,EAAaH,IAAb,CAAL,EAAyB;IACvB,MAAM,IAAIO,KAAJ,CAAW,eAAcP,IAAI,CAACK,IAAK,mBAAnC,CAAN;EACD;;EAED,OAAOL,IAAP;AACD"}