{"version": 3, "names": ["defineType", "visitor", "aliases", "fields", "process", "env", "BABEL_TYPES_8_BREAKING", "object", "validate", "Object", "assign", "oneOfNodeTypes", "callee", "assertNodeType", "key", "value", "expression", "builder", "body", "async", "assertValueType", "default", "exported", "properties", "chain", "assertEach", "elements"], "sources": ["../../src/definitions/experimental.ts"], "sourcesContent": ["import defineType, {\n  assertEach,\n  assertNodeType,\n  assertValueType,\n  chain,\n} from \"./utils\";\n\ndefineType(\"ArgumentPlaceholder\", {});\n\ndefineType(\"BindExpression\", {\n  visitor: [\"object\", \"callee\"],\n  aliases: [\"Expression\"],\n  fields: !process.env.BABEL_TYPES_8_BREAKING\n    ? {\n        object: {\n          validate: Object.assign(() => {}, {\n            oneOfNodeTypes: [\"Expression\"],\n          }),\n        },\n        callee: {\n          validate: Object.assign(() => {}, {\n            oneOfNodeTypes: [\"Expression\"],\n          }),\n        },\n      }\n    : {\n        object: {\n          validate: assertNodeType(\"Expression\"),\n        },\n        callee: {\n          validate: assertNodeType(\"Expression\"),\n        },\n      },\n});\n\ndefineType(\"ImportAttribute\", {\n  visitor: [\"key\", \"value\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    value: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n  },\n});\n\ndefineType(\"Decorator\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"DoExpression\", {\n  visitor: [\"body\"],\n  builder: [\"body\", \"async\"],\n  aliases: [\"Expression\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    async: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ExportDefaultSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"RecordExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ObjectProperty\", \"SpreadElement\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"TupleExpression\", {\n  fields: {\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Expression\", \"SpreadElement\")),\n      ),\n      default: [],\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"DecimalLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\n// https://github.com/tc39/proposal-js-module-blocks\ndefineType(\"ModuleExpression\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"Program\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-hack-pipes\ndefineType(\"TopicReference\", {\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-smart-pipes\ndefineType(\"PipelineTopicExpression\", {\n  builder: [\"expression\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelineBareFunction\", {\n  builder: [\"callee\"],\n  visitor: [\"callee\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelinePrimaryTopicReference\", {\n  aliases: [\"Expression\"],\n});\n"], "mappings": ";;AAAA;;AAOA,IAAAA,cAAA,EAAW,qBAAX,EAAkC,EAAlC;AAEA,IAAAA,cAAA,EAAW,gBAAX,EAA6B;EAC3BC,OAAO,EAAE,CAAC,QAAD,EAAW,QAAX,CADkB;EAE3BC,OAAO,EAAE,CAAC,YAAD,CAFkB;EAG3BC,MAAM,EAAE,CAACC,OAAO,CAACC,GAAR,CAAYC,sBAAb,GACJ;IACEC,MAAM,EAAE;MACNC,QAAQ,EAAEC,MAAM,CAACC,MAAP,CAAc,MAAM,CAAE,CAAtB,EAAwB;QAChCC,cAAc,EAAE,CAAC,YAAD;MADgB,CAAxB;IADJ,CADV;IAMEC,MAAM,EAAE;MACNJ,QAAQ,EAAEC,MAAM,CAACC,MAAP,CAAc,MAAM,CAAE,CAAtB,EAAwB;QAChCC,cAAc,EAAE,CAAC,YAAD;MADgB,CAAxB;IADJ;EANV,CADI,GAaJ;IACEJ,MAAM,EAAE;MACNC,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADJ,CADV;IAIED,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADJ;EAJV;AAhBuB,CAA7B;AA0BA,IAAAb,cAAA,EAAW,iBAAX,EAA8B;EAC5BC,OAAO,EAAE,CAAC,KAAD,EAAQ,OAAR,CADmB;EAE5BE,MAAM,EAAE;IACNW,GAAG,EAAE;MACHN,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf,EAA6B,eAA7B;IADP,CADC;IAINE,KAAK,EAAE;MACLP,QAAQ,EAAE,IAAAK,qBAAA,EAAe,eAAf;IADL;EAJD;AAFoB,CAA9B;AAYA,IAAAb,cAAA,EAAW,WAAX,EAAwB;EACtBC,OAAO,EAAE,CAAC,YAAD,CADa;EAEtBE,MAAM,EAAE;IACNa,UAAU,EAAE;MACVR,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADA;EADN;AAFc,CAAxB;AASA,IAAAb,cAAA,EAAW,cAAX,EAA2B;EACzBC,OAAO,EAAE,CAAC,MAAD,CADgB;EAEzBgB,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CAFgB;EAGzBf,OAAO,EAAE,CAAC,YAAD,CAHgB;EAIzBC,MAAM,EAAE;IACNe,IAAI,EAAE;MACJV,QAAQ,EAAE,IAAAK,qBAAA,EAAe,gBAAf;IADN,CADA;IAINM,KAAK,EAAE;MACLX,QAAQ,EAAE,IAAAY,sBAAA,EAAgB,SAAhB,CADL;MAELC,OAAO,EAAE;IAFJ;EAJD;AAJiB,CAA3B;AAeA,IAAArB,cAAA,EAAW,wBAAX,EAAqC;EACnCC,OAAO,EAAE,CAAC,UAAD,CAD0B;EAEnCC,OAAO,EAAE,CAAC,iBAAD,CAF0B;EAGnCC,MAAM,EAAE;IACNmB,QAAQ,EAAE;MACRd,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADF;EADJ;AAH2B,CAArC;AAUA,IAAAb,cAAA,EAAW,kBAAX,EAA+B;EAC7BC,OAAO,EAAE,CAAC,YAAD,CADoB;EAE7BC,OAAO,EAAE,CAAC,YAAD,CAFoB;EAG7BC,MAAM,EAAE;IACNoB,UAAU,EAAE;MACVf,QAAQ,EAAE,IAAAgB,YAAA,EACR,IAAAJ,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAK,iBAAA,EAAW,IAAAZ,qBAAA,EAAe,gBAAf,EAAiC,eAAjC,CAAX,CAFQ;IADA;EADN;AAHqB,CAA/B;AAaA,IAAAb,cAAA,EAAW,iBAAX,EAA8B;EAC5BG,MAAM,EAAE;IACNuB,QAAQ,EAAE;MACRlB,QAAQ,EAAE,IAAAgB,YAAA,EACR,IAAAJ,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAK,iBAAA,EAAW,IAAAZ,qBAAA,EAAe,YAAf,EAA6B,eAA7B,CAAX,CAFQ,CADF;MAKRQ,OAAO,EAAE;IALD;EADJ,CADoB;EAU5BpB,OAAO,EAAE,CAAC,UAAD,CAVmB;EAW5BC,OAAO,EAAE,CAAC,YAAD;AAXmB,CAA9B;AAcA,IAAAF,cAAA,EAAW,gBAAX,EAA6B;EAC3BiB,OAAO,EAAE,CAAC,OAAD,CADkB;EAE3Bd,MAAM,EAAE;IACNY,KAAK,EAAE;MACLP,QAAQ,EAAE,IAAAY,sBAAA,EAAgB,QAAhB;IADL;EADD,CAFmB;EAO3BlB,OAAO,EAAE,CAAC,YAAD,EAAe,SAAf,EAA0B,SAA1B,EAAqC,WAArC;AAPkB,CAA7B;AAWA,IAAAF,cAAA,EAAW,kBAAX,EAA+B;EAC7BC,OAAO,EAAE,CAAC,MAAD,CADoB;EAE7BE,MAAM,EAAE;IACNe,IAAI,EAAE;MACJV,QAAQ,EAAE,IAAAK,qBAAA,EAAe,SAAf;IADN;EADA,CAFqB;EAO7BX,OAAO,EAAE,CAAC,YAAD;AAPoB,CAA/B;AAYA,IAAAF,cAAA,EAAW,gBAAX,EAA6B;EAC3BE,OAAO,EAAE,CAAC,YAAD;AADkB,CAA7B;AAMA,IAAAF,cAAA,EAAW,yBAAX,EAAsC;EACpCiB,OAAO,EAAE,CAAC,YAAD,CAD2B;EAEpChB,OAAO,EAAE,CAAC,YAAD,CAF2B;EAGpCE,MAAM,EAAE;IACNa,UAAU,EAAE;MACVR,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADA;EADN,CAH4B;EAQpCX,OAAO,EAAE,CAAC,YAAD;AAR2B,CAAtC;AAWA,IAAAF,cAAA,EAAW,sBAAX,EAAmC;EACjCiB,OAAO,EAAE,CAAC,QAAD,CADwB;EAEjChB,OAAO,EAAE,CAAC,QAAD,CAFwB;EAGjCE,MAAM,EAAE;IACNS,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAA,EAAe,YAAf;IADJ;EADF,CAHyB;EAQjCX,OAAO,EAAE,CAAC,YAAD;AARwB,CAAnC;AAWA,IAAAF,cAAA,EAAW,+BAAX,EAA4C;EAC1CE,OAAO,EAAE,CAAC,YAAD;AADiC,CAA5C"}