{"version": 3, "names": ["defineType", "defineAliasedType", "visitor", "builder", "fields", "name", "validate", "assertNodeType", "expectedNode", "assertOneOf", "PLACEHOLDERS", "assertValueType"], "sources": ["../../src/definitions/misc.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  assertNodeType,\n  assertOneOf,\n  assertValueType,\n} from \"./utils\";\nimport { PLACEHOLDERS } from \"./placeholders\";\n\nconst defineType = defineAliasedType(\"Miscellaneous\");\n\nif (!process.env.BABEL_8_BREAKING) {\n  defineType(\"Noop\", {\n    visitor: [],\n  });\n}\n\ndefineType(\"Placeholder\", {\n  visitor: [],\n  builder: [\"expectedNode\", \"name\"],\n  // aliases: [], defined in placeholders.js\n  fields: {\n    name: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    expectedNode: {\n      validate: assertOneOf(...PLACEHOLDERS),\n    },\n  },\n});\n\ndefineType(\"V8IntrinsicIdentifier\", {\n  builder: [\"name\"],\n  fields: {\n    name: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n"], "mappings": ";;AAAA;;AAMA;;AAEA,MAAMA,UAAU,GAAG,IAAAC,wBAAA,EAAkB,eAAlB,CAAnB;AAEmC;EACjCD,UAAU,CAAC,MAAD,EAAS;IACjBE,OAAO,EAAE;EADQ,CAAT,CAAV;AAGD;AAEDF,UAAU,CAAC,aAAD,EAAgB;EACxBE,OAAO,EAAE,EADe;EAExBC,OAAO,EAAE,CAAC,cAAD,EAAiB,MAAjB,CAFe;EAIxBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAA,EAAe,YAAf;IADN,CADA;IAINC,YAAY,EAAE;MACZF,QAAQ,EAAE,IAAAG,kBAAA,EAAY,GAAGC,0BAAf;IADE;EAJR;AAJgB,CAAhB,CAAV;AAcAV,UAAU,CAAC,uBAAD,EAA0B;EAClCG,OAAO,EAAE,CAAC,MAAD,CADyB;EAElCC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAK,sBAAA,EAAgB,QAAhB;IADN;EADA;AAF0B,CAA1B,CAAV"}