{"version": 3, "names": ["PLACEHOLDERS", "PLACEHOLDERS_ALIAS", "Declaration", "Pattern", "type", "alias", "ALIAS_KEYS", "length", "PLACEHOLDERS_FLIPPED_ALIAS", "Object", "keys", "for<PERSON>ach", "hasOwnProperty", "call", "push"], "sources": ["../../src/definitions/placeholders.ts"], "sourcesContent": ["import { ALIAS_KEYS } from \"./utils\";\n\nexport const PLACEHOLDERS = [\n  \"Identifier\",\n  \"StringLiteral\",\n  \"Expression\",\n  \"Statement\",\n  \"Declaration\",\n  \"BlockStatement\",\n  \"ClassBody\",\n  \"Pattern\",\n] as const;\n\nexport const PLACEHOLDERS_ALIAS: Record<string, string[]> = {\n  Declaration: [\"Statement\"],\n  Pattern: [\"PatternLike\", \"LVal\"],\n};\n\nfor (const type of PLACEHOLDERS) {\n  const alias = ALIAS_KEYS[type];\n  if (alias?.length) PLACEHOLDERS_ALIAS[type] = alias;\n}\n\nexport const PLACEHOLDERS_FLIPPED_ALIAS: Record<string, string[]> = {};\n\nObject.keys(PLACEHOLDERS_ALIAS).forEach(type => {\n  PLACEHOLDERS_ALIAS[type].forEach(alias => {\n    if (!Object.hasOwnProperty.call(PLACEHOLDERS_FLIPPED_ALIAS, alias)) {\n      PLACEHOLDERS_FLIPPED_ALIAS[alias] = [];\n    }\n    PLACEHOLDERS_FLIPPED_ALIAS[alias].push(type);\n  });\n});\n"], "mappings": ";;;;;;;AAAA;;AAEO,MAAMA,YAAY,GAAG,CAC1B,YAD0B,EAE1B,eAF0B,EAG1B,YAH0B,EAI1B,WAJ0B,EAK1B,aAL0B,EAM1B,gBAN0B,EAO1B,WAP0B,EAQ1B,SAR0B,CAArB;;AAWA,MAAMC,kBAA4C,GAAG;EAC1DC,WAAW,EAAE,CAAC,WAAD,CAD6C;EAE1DC,OAAO,EAAE,CAAC,aAAD,EAAgB,MAAhB;AAFiD,CAArD;;;AAKP,KAAK,MAAMC,IAAX,IAAmBJ,YAAnB,EAAiC;EAC/B,MAAMK,KAAK,GAAGC,iBAAA,CAAWF,IAAX,CAAd;EACA,IAAIC,KAAJ,YAAIA,KAAK,CAAEE,MAAX,EAAmBN,kBAAkB,CAACG,IAAD,CAAlB,GAA2BC,KAA3B;AACpB;;AAEM,MAAMG,0BAAoD,GAAG,EAA7D;;AAEPC,MAAM,CAACC,IAAP,CAAYT,kBAAZ,EAAgCU,OAAhC,CAAwCP,IAAI,IAAI;EAC9CH,kBAAkB,CAACG,IAAD,CAAlB,CAAyBO,OAAzB,CAAiCN,KAAK,IAAI;IACxC,IAAI,CAACI,MAAM,CAACG,cAAP,CAAsBC,IAAtB,CAA2BL,0BAA3B,EAAuDH,KAAvD,CAAL,EAAoE;MAClEG,0BAA0B,CAACH,KAAD,CAA1B,GAAoC,EAApC;IACD;;IACDG,0BAA0B,CAACH,KAAD,CAA1B,CAAkCS,IAAlC,CAAuCV,IAAvC;EACD,CALD;AAMD,CAPD"}