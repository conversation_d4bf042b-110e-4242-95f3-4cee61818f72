{"version": 3, "names": ["inherits", "child", "parent", "key", "INHERIT_KEYS", "optional", "Object", "keys", "force", "inheritsComments"], "sources": ["../../src/modifications/inherits.ts"], "sourcesContent": ["import { INHERIT_KEYS } from \"../constants\";\nimport inheritsComments from \"../comments/inheritsComments\";\nimport type * as t from \"..\";\n\n/**\n * Inherit all contextual properties from `parent` node to `child` node.\n */\nexport default function inherits<T extends t.Node | null | undefined>(\n  child: T,\n  parent: t.Node | null | undefined,\n): T {\n  if (!child || !parent) return child;\n\n  // optionally inherit specific properties if not null\n  for (const key of INHERIT_KEYS.optional) {\n    // @ts-expect-error Fixme: refine parent types\n    if (child[key] == null) {\n      // @ts-expect-error Fixme: refine parent types\n      child[key] = parent[key];\n    }\n  }\n\n  // force inherit \"private\" properties\n  for (const key of Object.keys(parent)) {\n    if (key[0] === \"_\" && key !== \"__clone\") {\n      // @ts-expect-error Fixme: refine parent types\n      child[key] = parent[key];\n    }\n  }\n\n  // force inherit select properties\n  for (const key of INHERIT_KEYS.force) {\n    // @ts-expect-error Fixme: refine parent types\n    child[key] = parent[key];\n  }\n\n  inheritsComments(child, parent);\n\n  return child;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,QAAT,CACbC,KADa,EAEbC,MAFa,EAGV;EACH,IAAI,CAACD,KAAD,IAAU,CAACC,MAAf,EAAuB,OAAOD,KAAP;;EAGvB,KAAK,MAAME,GAAX,IAAkBC,uBAAA,CAAaC,QAA/B,EAAyC;IAEvC,IAAIJ,KAAK,CAACE,GAAD,CAAL,IAAc,IAAlB,EAAwB;MAEtBF,KAAK,CAACE,GAAD,CAAL,GAAaD,MAAM,CAACC,GAAD,CAAnB;IACD;EACF;;EAGD,KAAK,MAAMA,GAAX,IAAkBG,MAAM,CAACC,IAAP,CAAYL,MAAZ,CAAlB,EAAuC;IACrC,IAAIC,GAAG,CAAC,CAAD,CAAH,KAAW,GAAX,IAAkBA,GAAG,KAAK,SAA9B,EAAyC;MAEvCF,KAAK,CAACE,GAAD,CAAL,GAAaD,MAAM,CAACC,GAAD,CAAnB;IACD;EACF;;EAGD,KAAK,MAAMA,GAAX,IAAkBC,uBAAA,CAAaI,KAA/B,EAAsC;IAEpCP,KAAK,CAACE,GAAD,CAAL,GAAaD,MAAM,CAACC,GAAD,CAAnB;EACD;;EAED,IAAAM,yBAAA,EAAiBR,KAAjB,EAAwBC,MAAxB;EAEA,OAAOD,KAAP;AACD"}