{"version": 3, "names": ["CLEAR_KEYS", "CLEAR_KEYS_PLUS_COMMENTS", "COMMENT_KEYS", "removeProperties", "node", "opts", "map", "preserveComments", "key", "undefined", "Object", "keys", "symbols", "getOwnPropertySymbols", "sym"], "sources": ["../../src/modifications/removeProperties.ts"], "sourcesContent": ["import { COMMENT_KEYS } from \"../constants\";\nimport type * as t from \"..\";\n\nconst CLEAR_KEYS = [\n  \"tokens\", // only exist in t.File\n  \"start\",\n  \"end\",\n  \"loc\",\n  // Fixme: should be extra.raw / extra.rawValue?\n  \"raw\",\n  \"rawValue\",\n] as const;\n\nconst CLEAR_KEYS_PLUS_COMMENTS = [\n  ...COMMENT_KEYS,\n  \"comments\",\n  ...CLEAR_KEYS,\n] as const;\n\nexport type Options = { preserveComments?: boolean };\n/**\n * Remove all of the _* properties from a node along with the additional metadata\n * properties like location data and raw token data.\n */\nexport default function removeProperties(\n  node: t.Node,\n  opts: Options = {},\n): void {\n  const map = opts.preserveComments ? CLEAR_KEYS : CLEAR_KEYS_PLUS_COMMENTS;\n  for (const key of map) {\n    // @ts-expect-error tokens only exist in t.File\n    if (node[key] != null) node[key] = undefined;\n  }\n\n  for (const key of Object.keys(node)) {\n    // @ts-expect-error string can not index node\n    if (key[0] === \"_\" && node[key] != null) node[key] = undefined;\n  }\n\n  const symbols: Array<symbol> = Object.getOwnPropertySymbols(node);\n  for (const sym of symbols) {\n    // @ts-expect-error Fixme: document symbol properties\n    node[sym] = null;\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAGA,MAAMA,UAAU,GAAG,CACjB,QADiB,EAEjB,OAFiB,EAGjB,KAHiB,EAIjB,KAJiB,EAMjB,KANiB,EAOjB,UAPiB,CAAnB;AAUA,MAAMC,wBAAwB,GAAG,CAC/B,GAAGC,uBAD4B,EAE/B,UAF+B,EAG/B,GAAGF,UAH4B,CAAjC;;AAWe,SAASG,gBAAT,CACbC,IADa,EAEbC,IAAa,GAAG,EAFH,EAGP;EACN,MAAMC,GAAG,GAAGD,IAAI,CAACE,gBAAL,GAAwBP,UAAxB,GAAqCC,wBAAjD;;EACA,KAAK,MAAMO,GAAX,IAAkBF,GAAlB,EAAuB;IAErB,IAAIF,IAAI,CAACI,GAAD,CAAJ,IAAa,IAAjB,EAAuBJ,IAAI,CAACI,GAAD,CAAJ,GAAYC,SAAZ;EACxB;;EAED,KAAK,MAAMD,GAAX,IAAkBE,MAAM,CAACC,IAAP,CAAYP,IAAZ,CAAlB,EAAqC;IAEnC,IAAII,GAAG,CAAC,CAAD,CAAH,KAAW,GAAX,IAAkBJ,IAAI,CAACI,GAAD,CAAJ,IAAa,IAAnC,EAAyCJ,IAAI,CAACI,GAAD,CAAJ,GAAYC,SAAZ;EAC1C;;EAED,MAAMG,OAAsB,GAAGF,MAAM,CAACG,qBAAP,CAA6BT,IAA7B,CAA/B;;EACA,KAAK,MAAMU,GAAX,IAAkBF,OAAlB,EAA2B;IAEzBR,IAAI,CAACU,GAAD,CAAJ,GAAY,IAAZ;EACD;AACF"}