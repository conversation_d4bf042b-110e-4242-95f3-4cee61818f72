{"version": 3, "names": ["traverse", "node", "handlers", "state", "enter", "exit", "traverseSimpleImpl", "ancestors", "keys", "VISITOR_KEYS", "type", "key", "subNode", "Array", "isArray", "i", "length", "child", "push", "index", "pop"], "sources": ["../../src/traverse/traverse.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\nexport type TraversalAncestors = Array<{\n  node: t.Node;\n  key: string;\n  index?: number;\n}>;\n\nexport type TraversalHandler<T> = (\n  this: undefined,\n  node: t.Node,\n  parent: TraversalAncestors,\n  state: T,\n) => void;\n\nexport type TraversalHandlers<T> = {\n  enter?: TraversalHandler<T>;\n  exit?: TraversalHandler<T>;\n};\n\n/**\n * A general AST traversal with both prefix and postfix handlers, and a\n * state object. Exposes ancestry data to each handler so that more complex\n * AST data can be taken into account.\n */\nexport default function traverse<T>(\n  node: t.Node,\n  handlers: TraversalHandler<T> | TraversalHandlers<T>,\n  state?: T,\n): void {\n  if (typeof handlers === \"function\") {\n    handlers = { enter: handlers };\n  }\n\n  const { enter, exit } = handlers as TraversalHandlers<T>;\n\n  traverseSimpleImpl(node, enter, exit, state, []);\n}\n\nfunction traverseSimpleImpl<T>(\n  node: any,\n  enter: Function | undefined,\n  exit: Function | undefined,\n  state: T | undefined,\n  ancestors: TraversalAncestors,\n) {\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return;\n\n  if (enter) enter(node, ancestors, state);\n\n  for (const key of keys) {\n    const subNode = node[key];\n\n    if (Array.isArray(subNode)) {\n      for (let i = 0; i < subNode.length; i++) {\n        const child = subNode[i];\n        if (!child) continue;\n\n        ancestors.push({\n          node,\n          key,\n          index: i,\n        });\n\n        traverseSimpleImpl(child, enter, exit, state, ancestors);\n\n        ancestors.pop();\n      }\n    } else if (subNode) {\n      ancestors.push({\n        node,\n        key,\n      });\n\n      traverseSimpleImpl(subNode, enter, exit, state, ancestors);\n\n      ancestors.pop();\n    }\n  }\n\n  if (exit) exit(node, ancestors, state);\n}\n"], "mappings": ";;;;;;;AAAA;;AA0Be,SAASA,QAAT,CACbC,IADa,EAEbC,QAFa,EAGbC,KAHa,EAIP;EACN,IAAI,OAAOD,QAAP,KAAoB,UAAxB,EAAoC;IAClCA,QAAQ,GAAG;MAAEE,KAAK,EAAEF;IAAT,CAAX;EACD;;EAED,MAAM;IAAEE,KAAF;IAASC;EAAT,IAAkBH,QAAxB;EAEAI,kBAAkB,CAACL,IAAD,EAAOG,KAAP,EAAcC,IAAd,EAAoBF,KAApB,EAA2B,EAA3B,CAAlB;AACD;;AAED,SAASG,kBAAT,CACEL,IADF,EAEEG,KAFF,EAGEC,IAHF,EAIEF,KAJF,EAKEI,SALF,EAME;EACA,MAAMC,IAAI,GAAGC,yBAAA,CAAaR,IAAI,CAACS,IAAlB,CAAb;EACA,IAAI,CAACF,IAAL,EAAW;EAEX,IAAIJ,KAAJ,EAAWA,KAAK,CAACH,IAAD,EAAOM,SAAP,EAAkBJ,KAAlB,CAAL;;EAEX,KAAK,MAAMQ,GAAX,IAAkBH,IAAlB,EAAwB;IACtB,MAAMI,OAAO,GAAGX,IAAI,CAACU,GAAD,CAApB;;IAEA,IAAIE,KAAK,CAACC,OAAN,CAAcF,OAAd,CAAJ,EAA4B;MAC1B,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,OAAO,CAACI,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;QACvC,MAAME,KAAK,GAAGL,OAAO,CAACG,CAAD,CAArB;QACA,IAAI,CAACE,KAAL,EAAY;QAEZV,SAAS,CAACW,IAAV,CAAe;UACbjB,IADa;UAEbU,GAFa;UAGbQ,KAAK,EAAEJ;QAHM,CAAf;QAMAT,kBAAkB,CAACW,KAAD,EAAQb,KAAR,EAAeC,IAAf,EAAqBF,KAArB,EAA4BI,SAA5B,CAAlB;QAEAA,SAAS,CAACa,GAAV;MACD;IACF,CAfD,MAeO,IAAIR,OAAJ,EAAa;MAClBL,SAAS,CAACW,IAAV,CAAe;QACbjB,IADa;QAEbU;MAFa,CAAf;MAKAL,kBAAkB,CAACM,OAAD,EAAUR,KAAV,EAAiBC,IAAjB,EAAuBF,KAAvB,EAA8BI,SAA9B,CAAlB;MAEAA,SAAS,CAACa,GAAV;IACD;EACF;;EAED,IAAIf,IAAJ,EAAUA,IAAI,CAACJ,IAAD,EAAOM,SAAP,EAAkBJ,KAAlB,CAAJ;AACX"}