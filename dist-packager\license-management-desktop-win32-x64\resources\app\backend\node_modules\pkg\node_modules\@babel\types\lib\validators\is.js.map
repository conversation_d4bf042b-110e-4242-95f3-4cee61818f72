{"version": 3, "names": ["is", "type", "node", "opts", "matches", "isType", "FLIPPED_ALIAS_KEYS", "isPlaceholderType", "expectedNode", "shallowEqual"], "sources": ["../../src/validators/is.ts"], "sourcesContent": ["import shallowEqual from \"../utils/shallowEqual\";\nimport isType from \"./isType\";\nimport isPlaceholderType from \"./isPlaceholderType\";\nimport { FLIPPED_ALIAS_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function is<T extends t.Node[\"type\"]>(\n  type: T,\n  node: t.Node | null | undefined,\n  opts?: undefined,\n): node is Extract<t.Node, { type: T }>;\n\nexport default function is<\n  T extends t.Node[\"type\"],\n  P extends Extract<t.Node, { type: T }>,\n>(type: T, n: t.Node | null | undefined, required: Partial<P>): n is P;\n\nexport default function is<P extends t.Node>(\n  type: string,\n  node: t.Node | null | undefined,\n  opts: Partial<P>,\n): node is P;\n\nexport default function is(\n  type: string,\n  node: t.Node | null | undefined,\n  opts?: Partial<t.Node>,\n): node is t.Node;\n/**\n * Returns whether `node` is of given `type`.\n *\n * For better performance, use this instead of `is[Type]` when `type` is unknown.\n */\nexport default function is(\n  type: string,\n  node: t.Node | null | undefined,\n  opts?: Partial<t.Node>,\n): node is t.Node {\n  if (!node) return false;\n\n  const matches = isType(node.type, type);\n  if (!matches) {\n    if (!opts && node.type === \"Placeholder\" && type in FLIPPED_ALIAS_KEYS) {\n      // We can only return true if the placeholder doesn't replace a real node,\n      // but it replaces a category of nodes (an alias).\n      //\n      // t.is(\"Identifier\", node) gives some guarantees about node's shape, so we\n      // can't say that Placeholder(expectedNode: \"Identifier\") is an identifier\n      // because it doesn't have the same properties.\n      // On the other hand, t.is(\"Expression\", node) doesn't say anything about\n      // the shape of node because Expression can be many different nodes: we can,\n      // and should, safely report expression placeholders as Expressions.\n      return isPlaceholderType(node.expectedNode, type);\n    }\n    return false;\n  }\n\n  if (typeof opts === \"undefined\") {\n    return true;\n  } else {\n    return shallowEqual(node, opts);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AA8Be,SAASA,EAAT,CACbC,IADa,EAEbC,IAFa,EAGbC,IAHa,EAIG;EAChB,IAAI,CAACD,IAAL,EAAW,OAAO,KAAP;EAEX,MAAME,OAAO,GAAG,IAAAC,eAAA,EAAOH,IAAI,CAACD,IAAZ,EAAkBA,IAAlB,CAAhB;;EACA,IAAI,CAACG,OAAL,EAAc;IACZ,IAAI,CAACD,IAAD,IAASD,IAAI,CAACD,IAAL,KAAc,aAAvB,IAAwCA,IAAI,IAAIK,+BAApD,EAAwE;MAUtE,OAAO,IAAAC,0BAAA,EAAkBL,IAAI,CAACM,YAAvB,EAAqCP,IAArC,CAAP;IACD;;IACD,OAAO,KAAP;EACD;;EAED,IAAI,OAAOE,IAAP,KAAgB,WAApB,EAAiC;IAC/B,OAAO,IAAP;EACD,CAFD,MAEO;IACL,OAAO,IAAAM,qBAAA,EAAaP,IAAb,EAAmBC,IAAnB,CAAP;EACD;AACF"}