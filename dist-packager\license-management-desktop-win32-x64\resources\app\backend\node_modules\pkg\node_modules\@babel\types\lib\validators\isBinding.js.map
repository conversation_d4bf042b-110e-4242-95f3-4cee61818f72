{"version": 3, "names": ["isBinding", "node", "parent", "grandparent", "type", "keys", "getBindingIdentifiers", "i", "length", "key", "val", "Array", "isArray", "indexOf"], "sources": ["../../src/validators/isBinding.ts"], "sourcesContent": ["import getBindingIdentifiers from \"../retrievers/getBindingIdentifiers\";\nimport type * as t from \"..\";\n/**\n * Check if the input `node` is a binding identifier.\n */\nexport default function isBinding(\n  node: t.Node,\n  parent: t.Node,\n  grandparent?: t.Node,\n): boolean {\n  if (\n    grandparent &&\n    node.type === \"Identifier\" &&\n    parent.type === \"ObjectProperty\" &&\n    grandparent.type === \"ObjectExpression\"\n  ) {\n    // We need to special-case this, because getBindingIdentifiers\n    // has an ObjectProperty->value entry for destructuring patterns.\n    return false;\n  }\n\n  const keys =\n    // @ts-expect-error getBindingIdentifiers.keys does not cover all AST types\n    getBindingIdentifiers.keys[parent.type];\n  if (keys) {\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const val =\n        // @ts-expect-error key must present in parent\n        parent[key];\n      if (Array.isArray(val)) {\n        if (val.indexOf(node) >= 0) return true;\n      } else {\n        if (val === node) return true;\n      }\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;AAAA;;AAKe,SAASA,SAAT,CACbC,IADa,EAEbC,MAFa,EAGbC,WAHa,EAIJ;EACT,IACEA,WAAW,IACXF,IAAI,CAACG,IAAL,KAAc,YADd,IAEAF,MAAM,CAACE,IAAP,KAAgB,gBAFhB,IAGAD,WAAW,CAACC,IAAZ,KAAqB,kBAJvB,EAKE;IAGA,OAAO,KAAP;EACD;;EAED,MAAMC,IAAI,GAERC,8BAAA,CAAsBD,IAAtB,CAA2BH,MAAM,CAACE,IAAlC,CAFF;;EAGA,IAAIC,IAAJ,EAAU;IACR,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;MACpC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAD,CAAhB;MACA,MAAMG,GAAG,GAEPR,MAAM,CAACO,GAAD,CAFR;;MAGA,IAAIE,KAAK,CAACC,OAAN,CAAcF,GAAd,CAAJ,EAAwB;QACtB,IAAIA,GAAG,CAACG,OAAJ,CAAYZ,IAAZ,KAAqB,CAAzB,EAA4B,OAAO,IAAP;MAC7B,CAFD,MAEO;QACL,IAAIS,GAAG,KAAKT,IAAZ,EAAkB,OAAO,IAAP;MACnB;IACF;EACF;;EAED,OAAO,KAAP;AACD"}