{"version": 3, "names": ["isPlaceholderType", "placeholderType", "targetType", "aliases", "PLACEHOLDERS_ALIAS", "alias"], "sources": ["../../src/validators/isPlaceholderType.ts"], "sourcesContent": ["import { PLACEHOLDERS_ALIAS } from \"../definitions\";\n\n/**\n * Test if a `placeholderType` is a `targetType` or if `targetType` is an alias of `placeholderType`.\n */\nexport default function isPlaceholderType(\n  placeholderType: string,\n  targetType: string,\n): boolean {\n  if (placeholderType === targetType) return true;\n\n  const aliases: Array<string> | undefined =\n    PLACEHOLDERS_ALIAS[placeholderType];\n  if (aliases) {\n    for (const alias of aliases) {\n      if (targetType === alias) return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;AAAA;;AAKe,SAASA,iBAAT,CACbC,eADa,EAEbC,UAFa,EAGJ;EACT,IAAID,eAAe,KAAKC,UAAxB,EAAoC,OAAO,IAAP;EAEpC,MAAMC,OAAkC,GACtCC,+BAAA,CAAmBH,eAAnB,CADF;;EAEA,IAAIE,OAAJ,EAAa;IACX,KAAK,MAAME,KAAX,IAAoBF,OAApB,EAA6B;MAC3B,IAAID,UAAU,KAAKG,KAAnB,EAA0B,OAAO,IAAP;IAC3B;EACF;;EAED,OAAO,KAAP;AACD"}