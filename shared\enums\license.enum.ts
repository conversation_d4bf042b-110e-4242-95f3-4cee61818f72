export enum LicenseType {
  Online = 'online',
  Offline = 'offline'
}

export enum LicenseStatus {
  Active = 'active',
  Expired = 'expired',
  Revoked = 'revoked',
  Pending = 'pending'
}

export enum UserRole {
  Admin = 'admin',
  Manager = 'manager',
  User = 'user'
}

export enum ActivationError {
  InvalidProductKey = 'INVALID_PRODUCT_KEY',
  LicenseExpired = 'LICENSE_EXPIRED',
  LicenseRevoked = 'LICENSE_REVOKED',
  MachineIdMismatch = 'MACHINE_ID_MISMATCH',
  MaxActivationsReached = 'MAX_ACTIVATIONS_REACHED',
  ServerError = 'SERVER_ERROR',
  NetworkError = 'NETWORK_ERROR'
} 