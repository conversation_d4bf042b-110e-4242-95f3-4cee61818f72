export interface ILicense {
  id?: number;
  licenseKey: string;
  activationType: 'online' | 'offline';
  activatedAt?: Date;
  expiresAt?: Date;
  machineId?: string;
  isRevoked: boolean;
  userId?: number;
  softwareId?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ILicenseCreateRequest {
  activationType: 'online' | 'offline';
  expiresAt?: Date;
  userId: number;
  softwareId: number;
}

export interface ILicenseActivationRequest {
  productKey: string;
  machineId: string;
  activationType: 'online' | 'offline';
}

export interface ILicenseActivationResponse {
  success: boolean;
  licenseKey?: string;
  activationType?: 'online' | 'offline';
  expiresAt?: Date;
  message?: string;
}

export interface ILicenseValidationRequest {
  licenseKey: string;
  machineId?: string;
}

export interface ILicenseValidationResponse {
  success: boolean;
  isValid: boolean;
  expiresAt?: Date;
  message?: string;
}

export interface ILicenseUpdateRequest {
  activationType?: 'online' | 'offline';
  expiresAt?: Date;
  machineId?: string;
  isRevoked?: boolean;
} 