export interface ISoftware {
  id?: number;
  name: string;
  productKey: string;
  description?: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ISoftwareCreateRequest {
  name: string;
  productKey?: string;
  description?: string;
  isActive?: boolean;
}

export interface ISoftwareUpdateRequest {
  name?: string;
  productKey?: string;
  description?: string;
  isActive?: boolean;
} 