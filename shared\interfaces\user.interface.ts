export interface IUser {
  id?: number;
  username: string;
  email: string;
  passwordHash?: string;
  roles: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUserCreateRequest {
  username: string;
  email: string;
  password: string;
  roles?: string[];
}

export interface IUserLoginRequest {
  username: string;
  password: string;
}

export interface IUserLoginResponse {
  success: boolean;
  token?: string;
  user?: Omit<IUser, 'passwordHash'>;
  message?: string;
}

export interface IUserUpdateRequest {
  username?: string;
  email?: string;
  password?: string;
  roles?: string[];
} 