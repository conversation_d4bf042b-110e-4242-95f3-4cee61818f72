export class CryptoUtils {
  /**
   * 生成随机许可证密钥（客户端通用版本）
   */
  static generateLicenseKey(): string {
    const timestamp = Date.now().toString(36);
    const random = this.generateRandomHex(16);
    return `${timestamp}-${random}`.toUpperCase();
  }

  /**
   * 生成产品密钥
   */
  static generateProductKey(): string {
    const segments: string[] = [];
    for (let i = 0; i < 4; i++) {
      segments.push(this.generateRandomHex(4).toUpperCase());
    }
    return segments.join('-');
  }

  /**
   * 生成随机十六进制字符串
   */
  private static generateRandomHex(length: number): string {
    const chars = '0123456789abcdef';
    let result = '';
    for (let i = 0; i < length * 2; i++) {
      result += chars[Math.floor(Math.random() * 16)];
    }
    return result;
  }

  /**
   * 简单哈希函数（仅用于客户端验证）
   */
  static simpleHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 验证简单哈希值
   */
  static verifySimpleHash(data: string, hash: string): boolean {
    return CryptoUtils.simpleHash(data) === hash;
  }

  /**
   * Base64 编码
   */
  static encodeBase64(str: string): string {
    if (typeof window !== 'undefined' && window.btoa) {
      return window.btoa(str);
    }
    // Node.js environment fallback
    if (typeof global !== 'undefined' && global.Buffer) {
      return global.Buffer.from(str, 'utf8').toString('base64');
    }
    throw new Error('Base64 encoding not supported in this environment');
  }

  /**
   * Base64 解码
   */
  static decodeBase64(str: string): string {
    if (typeof window !== 'undefined' && window.atob) {
      return window.atob(str);
    }
    // Node.js environment fallback
    if (typeof global !== 'undefined' && global.Buffer) {
      return global.Buffer.from(str, 'base64').toString('utf8');
    }
    throw new Error('Base64 decoding not supported in this environment');
  }
} 