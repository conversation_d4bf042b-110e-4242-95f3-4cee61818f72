@echo off
echo ================================
echo   软件权限管理系统快速启动
echo ================================
echo.

echo 正在检查环境...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

echo Node.js 已找到
echo.

echo 进入后端目录...
cd /d "%~dp0backend"

echo 检查依赖是否已安装...
if not exist node_modules (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

echo 检查环境配置文件...
if not exist .env (
    echo 复制环境配置文件...
    copy config.example.env .env
    echo 请编辑 backend\.env 文件配置您的环境变量
)

echo.
echo 启动开发服务器...
echo 服务器将在 http://localhost:3000 启动
echo.
echo 按 Ctrl+C 停止服务器
echo.

npm run dev

pause 