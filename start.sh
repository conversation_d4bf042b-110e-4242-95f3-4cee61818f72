#!/bin/bash

echo "================================"
echo "   软件权限管理系统快速启动"
echo "================================"
echo

echo "正在检查环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

echo "Node.js 已找到"
echo

echo "进入后端目录..."
cd "$(dirname "$0")/backend"

echo "检查依赖是否已安装..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

echo "检查环境配置文件..."
if [ ! -f ".env" ]; then
    echo "复制环境配置文件..."
    cp config.example.env .env
    echo "请编辑 backend/.env 文件配置您的环境变量"
fi

echo
echo "启动开发服务器..."
echo "服务器将在 http://localhost:3000 启动"
echo
echo "按 Ctrl+C 停止服务器"
echo

npm run dev 